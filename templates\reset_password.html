{% extends "base.html" %}

{% block title %}Reset Password - Comsys Software PMO & CSO System{% endblock %}

{% block content %}
<div class="row justify-content-center mt-5">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h3><i class="bi bi-key me-2"></i>Reset Password</h3>
            </div>
            <div class="card-body p-4">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>{{ error }}
                </div>
                {% endif %}
                
                {% if success %}
                <div class="alert alert-success" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>{{ success }}
                    <div class="mt-2">
                        <a href="/login" class="btn btn-primary">Go to Login</a>
                    </div>
                </div>
                {% else %}
                <form action="/reset-password/{{ token }}" method="post">
                    <div class="mb-3">
                        <label for="password" class="form-label">New Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-key"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-key-fill"></i></span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Reset Password</button>
                    </div>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
