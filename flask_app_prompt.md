# Flask App Creation Prompt

Create a Flask web application that replicates the FastAPI app functionality with these requirements:

## Core Features:
1. **Authentication System**: Login, register, logout, password reset with email tokens
2. **User Management**: Admin panel for user CRUD operations with role-based permissions
3. **Database Models**: Users, Channels, Products, Sections, Status, Landlines with TKTS tracking
4. **CRUD Operations**: Add, edit, delete records for all entities with permission checks
5. **Dashboard**: Home page with statistics, charts, and data visualization
6. **Reports**: User activity logs and transaction reports

## Technical Stack:
- **Flask** with extensions: Flask-Login, Flask-SQLAlchemy, Flask-WTF, Flask-Mail
- **Database**: SQL Server with pyodbc connection
- **Templates**: Jinja2 (reuse existing templates in `templates/` folder)
- **Authentication**: Session-based with remember me functionality
- **Security**: Password hashing with bcrypt, CSRF protection, secure headers

## Database Models:
```python
# User model with permissions
class User(db.Model):
    __tablename__ = "Users"
    UserID = db.Column(db.Integer, primary_key=True)
    UserName = db.Column(db.String(100), unique=True, nullable=False)
    Email = db.Column(db.String(255), unique=True, nullable=False)
    Password = db.Column(db.String(255), nullable=True)
    IsActive = db.Column(db.Boolean, default=True)
    IsAdmin = db.Column(db.Boolean, default=False)
    CanView = db.Column(db.Boolean, default=True)
    CanAdd = db.Column(db.Boolean, default=False)
    CanEdit = db.Column(db.Boolean, default=False)
    CanDelete = db.Column(db.Boolean, default=False)
    ResetToken = db.Column(db.String(255), nullable=True)
    ResetTokenExpiry = db.Column(db.DateTime, nullable=True)

# Channel, Product, Section, Status, Landline models
# Transaction models: ChannelTKTS, ProductTKTS, SectionTKTS, StatusTKTS, LandlineCall
# UserLog model for activity tracking
```

## Routes Structure:
- `/` - Dashboard with statistics and charts
- `/login`, `/register`, `/logout` - Authentication
- `/forgot-password`, `/reset-password/<token>` - Password reset
- `/channels`, `/products`, `/sections`, `/status`, `/landlines` - CRUD pages
- `/users` - Admin user management
- `/reports` - Activity reports
- `/change-password` - User password change

## Key Features:
1. **Permission System**: Check CanView, CanAdd, CanEdit, CanDelete for each user
2. **Logging**: Track all user actions in UserLogs table
3. **Error Handling**: Global error handler with user-friendly messages
4. **Security Headers**: X-Content-Type-Options, X-Frame-Options, Cache-Control
5. **Form Validation**: Server-side validation with flash messages
6. **AJAX Support**: JSON responses for edit/delete operations
7. **Charts**: Chart.js integration for dashboard visualizations

## Database Connection:
```python
SQLALCHEMY_DATABASE_URI = "mssql+pyodbc://Rabea:Comsys$<EMAIL>/CSOStat?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes"
```

## Template Integration:
- Reuse existing templates: `base.html`, `login.html`, `home.html`, `channels.html`, etc.
- Update template context variables from FastAPI format to Flask format
- Replace `current_user` dependency injection with Flask-Login's `current_user`
- Convert `templates.TemplateResponse()` calls to `render_template()`

## Security Requirements:
- Hash passwords with bcrypt
- CSRF protection on all forms
- Session management with secure cookies
- SQL injection prevention with SQLAlchemy ORM
- XSS protection with template auto-escaping

Create a complete Flask application file that maintains the same functionality as the original FastAPI app while using Flask conventions and the existing template files.