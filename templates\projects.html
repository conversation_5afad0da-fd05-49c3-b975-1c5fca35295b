{% extends "base.html" %}

{% block title %}Tasks TKTS Form - Comsys Software PMO & CSO Systeme{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<!-- Form Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h2>Add New Task</h2>
    </div>
    <div class="card-body">
        <form action="/projects/submit" method="post">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="date" name="date" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="project_id" class="form-label">Project</label>
                    <select class="form-select" id="project_id" name="project_id" required>
                        <option value="" selected disabled>Select Project</option>
                        {% for project in projects %}
                        <option value="{{ project.ProjectID }}">{{ project.ProjectName }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="num_tasks" class="form-label">Number of Tasks</label>
                    <input type="number" class="form-control" id="num_tasks" name="num_tasks" min="0" required>
                </div>
            </div>
            <div class="text-end">
                <button type="submit" class="btn btn-primary" {% if not current_user.CanAdd %}disabled{% endif %}>
                    <i class="bi bi-plus-circle me-1"></i>Add Task
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Filter Card -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h2>Filter & Export</h2>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Date Filter -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">Date Range Filter</div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-5">
                                <label for="startDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="startDate" value="{{ first_day_of_month }}">
                            </div>
                            <div class="col-md-5">
                                <label for="endDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="endDate" value="{{ latest_date }}">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button class="btn btn-primary w-100" id="filterDateBtn">
                                    <i class="bi bi-funnel me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Search Filter -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">Search Filter</div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="Search...">
                            <button class="btn btn-primary" type="button" id="searchButton">
                                <i class="bi bi-search me-1"></i>Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Export Buttons -->
        <div class="row mt-2">
            <div class="col-12">
                <button class="btn btn-success me-2" id="exportBtn">
                    <i class="bi bi-file-excel me-1"></i>Export to Excel
                </button>
                <button class="btn btn-danger" id="printBtn">
                    <i class="bi bi-printer me-1"></i>Print
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h2>Dashboard</h2>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Summary Cards -->
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="card-title">Total Tasks</h3>
                        <h2 class="display-4" id="totalTasks">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="card-title">Projects</h3>
                        <h2 class="display-4" id="uniqueProjects">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="card-title">Avg Tasks/Day</h3>
                        <h2 class="display-4" id="avgTasks">0</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row mt-4">
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header">Project Distribution</div>
                    <div class="card-body d-flex align-items-center justify-content-center">
                        <canvas id="projectChart"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header">Task Trend</div>
                    <div class="card-body d-flex align-items-center justify-content-center">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Data Table -->
<div class="card">
    <div class="card-header bg-dark text-white">
        <div class="d-flex justify-content-between align-items-center">
            <h2>Project Tasks</h2>
            <span class="badge bg-primary" id="recordCount">{{ tasks|length }}</span>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="tasksTable">
                <thead>
                    <tr>
                        <th data-sort="id">ID</th>
                        <th data-sort="date">Date</th>
                        <th data-sort="project">Project</th>
                        <th data-sort="tasks">Tasks</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for task in tasks %}
                    <tr>
                        <td>{{ task.ID }}</td>
                        <td>{{ task.Date }}</td>
                        <td>{{ task.ProjectName }}</td>
                        <td>{{ task.Num_Tasks }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-primary edit-btn" data-id="{{ task.ID }}" data-date="{{ task.Date }}" data-project="{{ task.Project_ID }}" data-tasks="{{ task.Num_Tasks }}" {% if not current_user.CanEdit %}disabled{% endif %}>
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-danger delete-btn" data-id="{{ task.ID }}" {% if not current_user.CanDelete %}disabled{% endif %}>
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editModalLabel">Edit Project Task</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="edit_id" name="id">
                    <div class="mb-3">
                        <label for="edit_date" class="form-label">Date</label>
                        <input type="date" class="form-control" id="edit_date" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_project_id" class="form-label">Project</label>
                        <select class="form-select" id="edit_project_id" name="project_id" required>
                            {% for project in projects %}
                            <option value="{{ project.ProjectID }}">{{ project.ProjectName }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_num_tasks" class="form-label">Number of Tasks</label>
                        <input type="number" class="form-control" id="edit_num_tasks" name="num_tasks" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEditBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this task? This action cannot be undone.</p>
                <input type="hidden" id="delete_id">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const searchButton = document.getElementById('searchButton');
    const table = document.getElementById('tasksTable');
    const recordCount = document.getElementById('recordCount');
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    const filterDateBtn = document.getElementById('filterDateBtn');
    const printBtn = document.getElementById('printBtn');
    const exportBtn = document.getElementById('exportBtn');
    
    // Dashboard elements
    const totalTasksElement = document.getElementById('totalTasks');
    const uniqueProjectsElement = document.getElementById('uniqueProjects');
    const avgTasksElement = document.getElementById('avgTasks');
    const projectChartCtx = document.getElementById('projectChart')?.getContext('2d');
    const trendChartCtx = document.getElementById('trendChart')?.getContext('2d');
    
    let projectChart = null;
    let trendChart = null;
    
    // Set default date for new task to today
    const dateInput = document.getElementById('date');
    if (dateInput) {
        const today = new Date();
        const formattedToday = today.toISOString().split('T')[0];
        dateInput.value = formattedToday;
    }
    
    // Search functionality
    function filterTable() {
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const startDate = startDateInput && startDateInput.value ? new Date(startDateInput.value) : null;
        const endDate = endDateInput && endDateInput.value ? new Date(endDateInput.value) : null;
        
        if (!table) return;
        
        const rows = table.querySelectorAll('tbody tr');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const dateCell = row.cells[1].textContent;
            const rowDate = new Date(dateCell);
            
            let dateMatch = true;
            if (startDate && endDate) {
                // Set end date to end of day for inclusive comparison
                const endOfDay = new Date(endDate);
                endOfDay.setHours(23, 59, 59, 999);
                dateMatch = rowDate >= startDate && rowDate <= endOfDay;
            }
            
            if (text.includes(searchTerm) && dateMatch) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        if (recordCount) {
            recordCount.textContent = visibleCount;
        }
        updateDashboard();
    }
    
    if (searchInput) {
        searchInput.addEventListener('keyup', filterTable);
    }
    if (searchButton) {
        searchButton.addEventListener('click', filterTable);
    }
    if (filterDateBtn) {
        filterDateBtn.addEventListener('click', filterTable);
    }
    
    // Initial filter and dashboard update
    if (filterDateBtn) {
        // Trigger initial filter
        setTimeout(filterTable, 500);
    }
    
    // Update dashboard
    function updateDashboard() {
        if (!table) return;
        
        const rows = Array.from(table.querySelectorAll('tbody tr')).filter(row => row.style.display !== 'none');
        
        // Calculate total tasks
        const totalTasks = rows.reduce((sum, row) => sum + parseInt(row.cells[3].textContent || 0), 0);
        if (totalTasksElement) totalTasksElement.textContent = totalTasks;
        
        // Calculate unique projects
        const uniqueProjects = new Set(rows.map(row => row.cells[2].textContent)).size;
        if (uniqueProjectsElement) uniqueProjectsElement.textContent = uniqueProjects;
        
        // Calculate average tasks per day
        const uniqueDates = new Set(rows.map(row => row.cells[1].textContent)).size;
        const avgTasks = uniqueDates ? Math.round((totalTasks / uniqueDates) * 10) / 10 : 0;
        if (avgTasksElement) avgTasksElement.textContent = avgTasks;
        
        // Update charts
        updateCharts(rows);
    }
    
    // Update charts
    function updateCharts(rows) {
        // Project distribution chart
        if (projectChartCtx) {
            const projectData = {};
            rows.forEach(row => {
                const project = row.cells[2].textContent;
                const tasks = parseInt(row.cells[3].textContent || 0);
                projectData[project] = (projectData[project] || 0) + tasks;
            });
            
            const projectLabels = Object.keys(projectData);
            const projectValues = Object.values(projectData);
            
            if (projectChart) projectChart.destroy();
            
            projectChart = new Chart(projectChartCtx, {
                type: 'pie',
                data: {
                    labels: projectLabels,
                    datasets: [{
                        data: projectValues,
                        backgroundColor: [
                            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
                            '#5a5c69', '#858796', '#6f42c1', '#20c9a6', '#f8f9fc'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
        }
        
        // Trend chart
        if (trendChartCtx) {
            // Group by date
            const dateData = {};
            rows.forEach(row => {
                const date = row.cells[1].textContent;
                const tasks = parseInt(row.cells[3].textContent || 0);
                dateData[date] = (dateData[date] || 0) + tasks;
            });
            
            // Sort dates
            const sortedDates = Object.keys(dateData).sort((a, b) => new Date(a) - new Date(b));
            const trendValues = sortedDates.map(date => dateData[date]);
            
            if (trendChart) trendChart.destroy();
            
            trendChart = new Chart(trendChartCtx, {
                type: 'line',
                data: {
                    labels: sortedDates,
                    datasets: [{
                        label: 'Tasks',
                        data: trendValues,
                        borderColor: '#4e73df',
                        backgroundColor: 'rgba(78, 115, 223, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        }
                    }
                }
            });
        }
    }
    
    // Sorting
    sortHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const column = this.getAttribute('data-sort');
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            
            // Determine sort direction
            const currentDirection = this.getAttribute('data-direction') || 'asc';
            const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
            
            // Update direction attribute
            sortHeaders.forEach(h => h.removeAttribute('data-direction'));
            this.setAttribute('data-direction', newDirection);
            
            // Sort rows
            rows.sort((a, b) => {
                let aValue, bValue;
                
                if (column === 'id') {
                    aValue = parseInt(a.cells[0].textContent);
                    bValue = parseInt(b.cells[0].textContent);
                } else if (column === 'date') {
                    aValue = new Date(a.cells[1].textContent);
                    bValue = new Date(b.cells[1].textContent);
                } else if (column === 'project') {
                    aValue = a.cells[2].textContent;
                    bValue = b.cells[2].textContent;
                } else if (column === 'tasks') {
                    aValue = parseInt(a.cells[3].textContent);
                    bValue = parseInt(b.cells[3].textContent);
                }
                
                if (newDirection === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });
            
            // Reorder table
            const tbody = table.querySelector('tbody');
            rows.forEach(row => tbody.appendChild(row));
            
            // Update visual indicators
            sortHeaders.forEach(h => h.querySelector('i')?.remove());
            const icon = document.createElement('i');
            icon.className = newDirection === 'asc' ? 'bi bi-sort-up ms-1' : 'bi bi-sort-down ms-1';
            this.appendChild(icon);
        });
    });
    
    // Print functionality
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            window.print();
        });
    }
    
    // Export to Excel
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            if (!table) return;
            
            const rows = Array.from(table.querySelectorAll('tbody tr')).filter(row => row.style.display !== 'none');
            const headers = Array.from(table.querySelectorAll('thead th')).slice(0, 4).map(th => th.textContent);
            
            const data = [headers];
            rows.forEach(row => {
                const rowData = [];
                for (let i = 0; i < 4; i++) {
                    rowData.push(row.cells[i].textContent);
                }
                data.push(rowData);
            });
            
            const ws = XLSX.utils.aoa_to_sheet(data);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Project Tasks');
            
            const today = new Date().toISOString().split('T')[0];
            XLSX.writeFile(wb, `Project_Tasks_${today}.xlsx`);
        });
    }
    
    // Edit task
    const editModal = document.getElementById('editModal') ? new bootstrap.Modal(document.getElementById('editModal')) : null;
    const editButtons = document.querySelectorAll('.edit-btn');
    const saveEditBtn = document.getElementById('saveEditBtn');
    
    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const date = this.getAttribute('data-date');
            const project = this.getAttribute('data-project');
            const tasks = this.getAttribute('data-tasks');
            
            if (editModal) {
                document.getElementById('edit_id').value = id;
                document.getElementById('edit_date').value = date;
                document.getElementById('edit_project_id').value = project;
                document.getElementById('edit_num_tasks').value = tasks;
                
                editModal.show();
            }
        });
    });
    
    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', function() {
            const form = document.getElementById('editForm');
            if (!form) return;
            
            const formData = new FormData(form);
            
            fetch('/projects/update', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    // Refresh the page to show updated data
                    window.location.reload();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.detail || 'Error updating task');
                    });
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        });
    }
    
    // Delete task
    const deleteModal = document.getElementById('deleteModal') ? new bootstrap.Modal(document.getElementById('deleteModal')) : null;
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            if (deleteModal) {
                document.getElementById('delete_id').value = id;
                deleteModal.show();
            }
        });
    });
    
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const id = document.getElementById('delete_id').value;
            
            fetch(`/projects/delete/${id}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (response.ok) {
                    // Refresh the page to show updated data
                    window.location.reload();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.detail || 'Error deleting task');
                    });
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        });
    }
});
</script>
{% endblock %}

