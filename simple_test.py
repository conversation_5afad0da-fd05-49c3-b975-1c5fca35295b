#!/usr/bin/env python3
"""
Simple test to verify JWT is working
"""

import jwt
from datetime import datetime, timedelta

# Test JWT functionality
SECRET_KEY = "YOUR_SECRET_KEY_HERE"
ALGORITHM = "HS256"

print("Testing JWT functionality...")

# Create test token
test_data = {
    "sub": "admin",
    "exp": datetime.utcnow() + timedelta(minutes=30)
}

try:
    # Test encoding
    token = jwt.encode(test_data, SECRET_KEY, algorithm=ALGORITHM)
    print(f"✅ JWT encode successful: {token[:50]}...")
    
    # Test decoding
    decoded = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
    print(f"✅ JWT decode successful: {decoded}")
    
    print("🎉 JWT is working correctly!")
    
except Exception as e:
    print(f"❌ JWT test failed: {e}")
    import traceback
    traceback.print_exc()
