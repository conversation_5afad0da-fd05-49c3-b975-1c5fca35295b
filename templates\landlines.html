{% extends "base.html" %}

{% block title %}Landline Calls - Comsys Software Daily Reports Module{% endblock %}

{% block content %}
<!-- Form Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h2>Add New Landline Call</h2>
    </div>
    <div class="card-body">
        <form action="/landlines/submit" method="post">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="date" name="date" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="landline_id" class="form-label">Landline</label>
                    <select class="form-select" id="landline_id" name="landline_id" required>
                        <option value="" selected disabled>Select Landline</option>
                        {% for landline in landlines %}
                        <option value="{{ landline.id }}">{{ landline.Landline }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="calls_count" class="form-label">Number of Calls</label>
                    <input type="number" class="form-control" id="calls_count" name="calls_count" min="0" required>
                </div>
            </div>
            <div class="text-end">
                <button type="submit" class="btn btn-primary" {% if not current_user.CanAdd %}disabled{% endif %}>
                    <i class="bi bi-plus-circle me-2"></i>Add Call Record
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Filter Card -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h2>Filter & Export</h2>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Date Filter -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">Date Range Filter</div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-5">
                                <label for="startDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-5">
                                <label for="endDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button class="btn btn-primary w-100" id="filterDateBtn">
                                    <i class="bi bi-funnel me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Search & Export -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">Search & Export</div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-6">
                                <label for="searchInput" class="form-label">Search</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchInput" placeholder="Search...">
                                    <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <div class="btn-group w-100">
                                    <button class="btn btn-success" id="exportBtn">
                                        <i class="bi bi-file-excel me-1"></i>Export
                                    </button>
                                    <button class="btn btn-info" id="printBtn">
                                        <i class="bi bi-printer me-1"></i>Print
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Grid -->
<div class="card">
    <div class="card-header bg-secondary text-white">
        <h2>Call History</h2>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="transactionsTable">
                <thead>
                    <tr>
                        <th data-sort="id">ID <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="date">Date <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="landline">Landline <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="calls">Calls <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for call in calls %}
                    <tr>
                        <td>{{ call.ID }}</td>
                        <td>{{ call.Date.strftime('%Y-%m-%d') if call.Date is not string else call.Date }}</td>
                        <td>{{ call.Landline }}</td>
                        <td>{{ call.CallsCount }}</td>
                        <td>
                            <button class="btn btn-sm btn-warning edit-btn" 
                                    data-id="{{ call.ID }}"
                                    data-date="{{ call.Date.strftime('%Y-%m-%d') if call.Date is not string else call.Date }}" 
                                    data-landline-id="{{ call.LandlineNum }}"
                                    data-calls="{{ call.CallsCount }}"
                                    {% if not current_user.CanEdit %}disabled{% endif %}>
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-btn" 
                                    data-id="{{ call.ID }}"
                                    {% if not current_user.CanDelete %}disabled{% endif %}>
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-secondary fw-bold">
                        <td colspan="3" class="text-end">Total:</td>
                        <td id="gridTotalTkts">0</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="card-footer bg-light">
        <strong>Total Records: <span id="recordCount">{{ calls|length }}</span></strong>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editModalLabel">Edit Call Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="edit_id" name="id">
                    <div class="mb-3">
                        <label for="edit_date" class="form-label">Date:</label>
                        <input type="date" class="form-control" id="edit_date" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_landline_id" class="form-label">Landline:</label>
                        <select class="form-select" id="edit_landline_id" name="landline_id" required>
                            <option value="" selected disabled>Select Landline</option>
                            {% for landline in landlines %}
                            <option value="{{ landline.id }}">{{ landline.Landline }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_calls_count" class="form-label">Number of Calls:</label>
                        <input type="number" class="form-control" id="edit_calls_count" name="calls_count" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveChangesBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Delete Call Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this call record? This action cannot be undone.</p>
                <input type="hidden" id="delete_id" name="id">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM fully loaded');
        
        // Debug: Print all edit buttons and their data-id attributes
        const allEditBtns = document.querySelectorAll('.edit-btn');
        console.log('Found', allEditBtns.length, 'edit buttons');
        allEditBtns.forEach((btn, index) => {
            console.log(`Edit button ${index} data-id:`, btn.getAttribute('data-id'));
        });
        
        // Debug: Print all delete buttons and their data-id attributes
        const allDeleteBtns = document.querySelectorAll('.delete-btn');
        console.log('Found', allDeleteBtns.length, 'delete buttons');
        allDeleteBtns.forEach((btn, index) => {
            console.log(`Delete button ${index} data-id:`, btn.getAttribute('data-id'));
        });
        
        const searchInput = document.getElementById('searchInput');
        const searchButton = document.getElementById('searchButton');
        const table = document.getElementById('transactionsTable');
        const recordCount = document.getElementById('recordCount');
        const sortHeaders = document.querySelectorAll('th[data-sort]');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        const filterDateBtn = document.getElementById('filterDateBtn');
        const printBtn = document.getElementById('printBtn');
        const exportBtn = document.getElementById('exportBtn');
        
        // Find the latest transaction date
        let latestDate = new Date().toISOString().split('T')[0]; // Default to today
        if (table) {
            const dateColumn = 1; // Date is in the second column (index 1)
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 0) {
                // Get all dates from the table
                const dates = Array.from(rows).map(row => new Date(row.cells[dateColumn].textContent));
                // Find the latest date
                if (dates.length > 0) {
                    const maxDate = new Date(Math.max.apply(null, dates));
                    latestDate = maxDate.toISOString().split('T')[0];
                }
            }
        }
        
        // Set date inputs - both start and end date to latest transaction date
        if (startDateInput) {
            startDateInput.value = latestDate;
        }
        
        if (endDateInput) {
            endDateInput.value = latestDate;
        }
        
        // Set default date for new transaction to today
        const dateInput = document.getElementById('date');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
        
        // Search functionality
        function filterTable() {
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const startDate = startDateInput && startDateInput.value ? new Date(startDateInput.value) : null;
            const endDate = endDateInput && endDateInput.value ? new Date(endDateInput.value) : null;
            
            if (!table) return;
            
            const rows = table.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const dateCell = row.cells[1].textContent;
                const rowDate = new Date(dateCell);
                
                let dateMatch = true;
                if (startDate && endDate) {
                    // Set end date to end of day for inclusive filtering
                    const endOfDay = new Date(endDate);
                    endOfDay.setHours(23, 59, 59, 999);
                    dateMatch = rowDate >= startDate && rowDate <= endOfDay;
                }
                
                const textMatch = searchTerm === '' || text.includes(searchTerm);
                
                if (dateMatch && textMatch) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            if (recordCount) {
                recordCount.textContent = visibleCount;
            }
        }
        
        // Attach search event listeners
        if (searchInput) {
            searchInput.addEventListener('keyup', filterTable);
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', filterTable);
        }
        
        // Date filter button
        if (filterDateBtn) {
            filterDateBtn.addEventListener('click', filterTable);
        }
        
        // Sorting functionality
        let currentSort = { column: 'date', direction: 'desc' };
        
        function sortTable(column) {
            if (!table) return;
            
            // Toggle direction if same column is clicked
            if (currentSort.column === column) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.column = column;
                currentSort.direction = 'asc';
            }
            
            // Update sort icons
            sortHeaders.forEach(header => {
                const headerColumn = header.getAttribute('data-sort');
                const icon = header.querySelector('.sort-icon');
                
                if (headerColumn === column) {
                    icon.className = 'bi sort-icon ' + 
                        (currentSort.direction === 'asc' ? 'bi-arrow-up' : 'bi-arrow-down');
                } else {
                    icon.className = 'bi bi-arrow-down-up sort-icon';
                }
            });
            
            // Get rows and sort
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            const sortedRows = rows.sort((a, b) => {
                let aValue, bValue;
                
                switch (column) {
                    case 'id':
                        aValue = parseInt(a.cells[0].textContent);
                        bValue = parseInt(b.cells[0].textContent);
                        break;
                    case 'date':
                        aValue = new Date(a.cells[1].textContent);
                        bValue = new Date(b.cells[1].textContent);
                        break;
                    case 'landline':
                        aValue = a.cells[2].textContent;
                        bValue = b.cells[2].textContent;
                        break;
                    case 'calls':
                        aValue = parseInt(a.cells[3].textContent);
                        bValue = parseInt(b.cells[3].textContent);
                        break;
                    default:
                        return 0;
                }
                
                if (currentSort.direction === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });
            
            // Reorder table
            const tbody = table.querySelector('tbody');
            sortedRows.forEach(row => tbody.appendChild(row));
        }
        
        // Attach sort event listeners
        sortHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const column = this.getAttribute('data-sort');
                sortTable(column);
            });
        });
        
        // Edit functionality
        const editBtns = document.querySelectorAll('.edit-btn');
        const editModal = document.getElementById('editModal');
        const editForm = document.getElementById('editForm');
        const editDate = document.getElementById('edit_date');
        const editLandlineId = document.getElementById('edit_landline_id');
        const editCallsCount = document.getElementById('edit_calls_count');
        const saveChangesBtn = document.getElementById('saveChangesBtn');

        // Add debugging for edit button clicks
        editBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const date = this.getAttribute('data-date');
                const landlineId = this.getAttribute('data-landline-id');
                const calls = this.getAttribute('data-calls');
                
                console.log('Edit button clicked with ID:', id);
                console.log('Date:', date);
                console.log('Landline ID:', landlineId);
                console.log('Calls:', calls);
                
                document.getElementById('edit_id').value = id;
                editDate.value = date;
                editLandlineId.value = landlineId;
                editCallsCount.value = calls;
                
                const modal = new bootstrap.Modal(editModal);
                modal.show();
            });
        });

        if (saveChangesBtn) {
            saveChangesBtn.addEventListener('click', function() {
                const formData = new FormData(editForm);
                const id = document.getElementById('edit_id').value;
                
                if (!id) {
                    alert('Error: Record ID is missing');
                    return;
                }
                
                fetch(`/landlines/update/${id}`, {
                    method: 'PUT',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.message || `HTTP error! status: ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        window.location.reload();
                    } else {
                        alert('Error updating record: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error: ' + error.message);
                });
            });
        }

        // Delete functionality
        const deleteBtns = document.querySelectorAll('.delete-btn');
        const deleteModal = document.getElementById('deleteModal');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');

        // Add debugging for delete button clicks
        deleteBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                console.log('Delete button clicked with ID:', id);
                
                document.getElementById('delete_id').value = id;
                
                const modal = new bootstrap.Modal(deleteModal);
                modal.show();
            });
        });

        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', function() {
                const id = document.getElementById('delete_id').value;
                
                if (!id) {
                    alert('Error: Record ID is missing');
                    return;
                }
                
                fetch(`/landlines/delete/${id}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(data => {
                            throw new Error(data.message || `HTTP error! status: ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        window.location.reload();
                    } else {
                        alert('Error deleting record: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error: ' + error.message);
                });
            });
        }
        
        // Print functionality
        if (printBtn) {
            printBtn.addEventListener('click', function() {
                window.print();
            });
        }
        
        // Export to Excel functionality
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                if (!table) return;
                
                // Get visible rows only
                const visibleRows = Array.from(table.querySelectorAll('tbody tr'))
                    .filter(row => row.style.display !== 'none');
                
                // Create worksheet data
                const wsData = [
                    ['ID', 'Date', 'Landline', 'Calls'] // Header row
                ];
                
                visibleRows.forEach(row => {
                    const rowData = [];
                    // Skip the actions column
                    for (let i = 0; i < 4; i++) {
                        rowData.push(row.cells[i].textContent);
                    }
                    wsData.push(rowData);
                });
                
                // Create workbook
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(wsData);
                
                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'Landline Calls');
                
                // Generate Excel file and trigger download
                const today = new Date().toISOString().split('T')[0];
                XLSX.writeFile(wb, `Landline_Calls_${today}.xlsx`);
            });
        }
        
        // Update grid totals specifically for this page
        function updateGridTotals() {
            const table = document.getElementById('transactionsTable');
            if (!table) return;
            
            const rows = Array.from(table.querySelectorAll('tbody tr'))
                .filter(row => row.style.display !== 'none');
            
            // Calculate total TKTs
            let totalTkts = 0;
            rows.forEach(row => {
                if (row.cells.length > 3) {
                    const tktsValue = row.cells[3].textContent.trim();
                    if (tktsValue && !isNaN(tktsValue)) {
                        totalTkts += parseInt(tktsValue);
                    }
                }
            });
            
            // Update grid total element
            const gridTotalTkts = document.getElementById('gridTotalTkts');
            if (gridTotalTkts) {
                gridTotalTkts.textContent = totalTkts;
            }
        }
        
        // Initial update of grid totals
        setTimeout(updateGridTotals, 500);
        
        // Add event listeners for filter elements
        const filterElements = [
            document.getElementById('searchInput'),
            document.getElementById('filterDateBtn'),
            document.getElementById('resetFilterBtn')
        ];
        
        filterElements.forEach(element => {
            if (element) {
                if (element.tagName === 'BUTTON') {
                    element.addEventListener('click', function() {
                        setTimeout(updateGridTotals, 100);
                    });
                } else {
                    element.addEventListener('change', function() {
                        setTimeout(updateGridTotals, 100);
                    });
                }
            }
        });
        
        // Initial sort by date (newest first)
        sortTable('date');
        
        // Initial filter
        filterTable();
    });
</script>
{% endblock %}






