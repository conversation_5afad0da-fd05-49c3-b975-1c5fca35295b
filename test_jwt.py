#!/usr/bin/env python3
"""
Test script to verify JWT functionality
"""

import jwt
from datetime import datetime, timedelta

# Test JWT encoding and decoding
SECRET_KEY = "test_secret_key"
ALGORITHM = "HS256"

def test_jwt():
    print("Testing JWT functionality...")
    
    # Test data
    test_data = {
        "sub": "test_user",
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    
    try:
        # Test encoding
        print("Testing JWT encoding...")
        token = jwt.encode(test_data, SECRET_KEY, algorithm=ALGORITHM)
        print(f"✅ JWT encoding successful. Token: {token[:50]}...")
        
        # Test decoding
        print("Testing JWT decoding...")
        decoded = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        print(f"✅ JWT decoding successful. Decoded data: {decoded}")
        
        print("🎉 All JWT tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ JWT test failed: {e}")
        return False

if __name__ == "__main__":
    test_jwt()
