{% extends "base.html" %}

{% block title %}Change Password - Comsys Software Daily Reports Module{% endblock %}

{% block content %}
<div class="row justify-content-center mt-5">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h3><i class="bi bi-key me-2"></i>Change Password</h3>
            </div>
            <div class="card-body p-4">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>{{ error }}
                </div>
                {% endif %}
                
                {% if success %}
                <div class="alert alert-success" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>{{ success }}
                    <div class="mt-2">
                        <a href="/login" class="btn btn-primary">Go to Login</a>
                    </div>
                </div>
                {% else %}
                <form action="/change-password" method="post">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                            <!--input type="email" class="form-control" id="email" name="email" required-->
                            <input type="email" class="form-control" id="email" name="email" required readonly>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-key"></i></span>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-key"></i></span>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-key-fill"></i></span>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Change Password</button>
                    </div>
                </form>
                {% endif %}
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0"><a href="/login" class="text-decoration-none">Back to Login</a></p>
            </div>
        </div>
    </div>
</div>

<script>
    window.addEventListener("DOMContentLoaded", () => {
        const params = new URLSearchParams(window.location.search);
        const email = params.get("email");
        if (email) {
            const emailInput = document.getElementById("email");
            if (emailInput) {
                emailInput.value = email;
            }
        }
    });
</script>
{% endblock %}
