{% extends "base.html" %}

{% block title %}Forgot Password - Comsys Software Daily Reports Module{% endblock %}

{% block content %}
<div class="row justify-content-center mt-5">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h3><i class="bi bi-key me-2"></i>Forgot Password</h3>
            </div>
            <div class="card-body p-4">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>{{ error }}
                </div>
                {% endif %}
                
                {% if success %}
                <div class="alert alert-success" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>{{ success }}
                    <div class="mt-3">
                        <p class="text-muted">
                            <strong>Note:</strong> In development mode, the reset link is logged to the console instead of being sent by email. 
                            Please check the server logs to find your reset link.
                        </p>
                    </div>
                </div>
                {% endif %}
                
                <form action="/forgot-password" method="post">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="form-text">Enter your email address and we'll send you a link to reset your password.</div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Send Reset Link</button>
                        <a href="/login" class="btn btn-outline-secondary">Back to Login</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}



