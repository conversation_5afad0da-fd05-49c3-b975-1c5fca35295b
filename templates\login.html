{% extends "base.html" %}


{% block content %}
<div class="d-flex align-items-center justify-content-center" style="min-height: 50vh;">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center py-3">
                <h3><i class="bi bi-box-arrow-in-right me-2"></i>Login</h3>
            </div>
            <div class="card-body p-4">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>{{ error }}
                </div>
                {% endif %}
                
                {% if request.query_params.get('registered') == 'true' %}
                <div class="alert alert-success" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>Registration successful! Please login with your credentials.
                </div>
                {% endif %}
                
                <form action="/login" method="post">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-key"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remember" name="remember" value="true">
                            <label class="form-check-label" for="remember">Remember me</label>
                        </div>
                        <div>
                            <a href="#" class="text-decoration-none" onclick="redirectToChangePassword()">Change password</a>
                        </div>
                    </div>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center py-3">
                <p class="mb-0">Don't have an account? <a href="/register" class="text-decoration-none">Register</a></p>
            </div>
        </div>
    </div>
</div>

<script>
    // Load remembered credentials
    window.onload = function () {
        const savedEmail = localStorage.getItem("rememberedEmail");
        const savedPassword = localStorage.getItem("rememberedPassword");
        const rememberChecked = localStorage.getItem("rememberMeChecked");

        if (rememberChecked === "true") {
            if (savedEmail) document.getElementById("email").value = savedEmail;
            if (savedPassword) document.getElementById("password").value = savedPassword;
            document.getElementById("remember").checked = true;
        }
    };

    document.addEventListener("DOMContentLoaded", () => {
        const form = document.querySelector("form");
        form.addEventListener("submit", function () {
            if (document.getElementById("remember").checked) {
                localStorage.setItem("rememberedEmail", document.getElementById("email").value);
                localStorage.setItem("rememberedPassword", document.getElementById("password").value);
                localStorage.setItem("rememberMeChecked", true);
            } else {
                localStorage.removeItem("rememberedEmail");
                localStorage.removeItem("rememberedPassword");
                localStorage.removeItem("rememberMeChecked");
            }
        });
    });

    function redirectToChangePassword() {
        const email = document.getElementById("email").value;
        if (email) {
            window.location.href = `/change-password?email=${encodeURIComponent(email)}`;
        } else {
            alert("Please enter your email before changing password.");
        }
    }
</script>
{% endblock %}
