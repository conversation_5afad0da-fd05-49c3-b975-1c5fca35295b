{% extends "base.html" %}

{% block title %}Employee TKTS Form - Comsys Software PMO & CSO System{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<!-- Form Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h2>Add New Employee Transaction</h2>
    </div>
    <div class="card-body">
        <form action="/employees/submit" method="post">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="date" name="date" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="agent_id" class="form-label">Employee</label>
                    <select class="form-select" id="agent_id" name="agent_id" required>
                        <option value="" selected disabled>Select Employee</option>
                        {% for agent in agents %}
                        <option value="{{ agent.Agent_Id }}">{{ agent.Agent_Name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="num_tkts" class="form-label">Number of TKTS</label>
                    <input type="number" class="form-control" id="num_tkts" name="num_tkts" min="0" required>
                </div>
            </div>
            <div class="text-end">
                <button type="submit" class="btn btn-primary" {% if not current_user.CanAdd %}disabled{% endif %}>
                    <i class="bi bi-plus-circle me-2"></i>Add Transaction
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Filter Card -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h2>Filter & Export</h2>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Date Filter -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">Date Range Filter</div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-5">
                                <label for="startDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-5">
                                <label for="endDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button class="btn btn-primary w-100" id="filterDateBtn">
                                    <i class="bi bi-funnel me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Search -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">Search & Export</div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-8">
                                <label for="searchInput" class="form-label">Search</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchInput" placeholder="Search...">
                                    <button class="btn btn-primary" type="button" id="searchButton">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <div class="btn-group w-100">
                                    <button class="btn btn-success" id="exportBtn">
                                        <i class="bi bi-file-excel me-1"></i>Export
                                    </button>
                                    <button class="btn btn-info" id="printBtn">
                                        <i class="bi bi-printer me-1"></i>Print
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Grid -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h2>Transactions History</h2>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="transactionsTable">
                <thead>
                    <tr>
                        <th data-sort="id">ID <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="date">Date <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="agent">Employee <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="tkts">TKTS <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.ID }}</td>
                        <td>{{ transaction.Date.strftime('%Y-%m-%d') if transaction.Date is not string else transaction.Date }}</td>
                        <td>{{ transaction.Agent_Name }}</td>
                        <td>{{ transaction.Num_TKTS }}</td>
                        <td>
                            <button class="btn btn-sm btn-warning edit-btn" data-id="{{ transaction.ID }}" 
                                    data-date="{{ transaction.Date.strftime('%Y-%m-%d') }}" 
                                    data-agent="{{ transaction.Agent_Name }}"
                                    data-agent-id="{{ transaction.Agent_Id }}"
                                    data-tkts="{{ transaction.Num_TKTS }}"
                                    {% if not current_user.CanEdit %}disabled{% endif %}>
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-btn" data-id="{{ transaction.ID }}"
                                    {% if not current_user.CanDelete %}disabled{% endif %}>
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-secondary fw-bold">
                        <td colspan="3" class="text-end">Total:</td>
                        <td id="gridTotalTkts">0</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="card-footer bg-light">
        <strong>Total Records: <span id="recordCount">{{ transactions|length }}</span></strong>
    </div>
</div>



<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editModalLabel">Edit Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" action="/employees/update" method="post">
                    <input type="hidden" id="edit_id" name="id">
                    <div class="mb-3">
                        <label for="edit_date" class="form-label">Date:</label>
                        <input type="date" class="form-control" id="edit_date" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_agent_id" class="form-label">Employee:</label>
                        <select class="form-select" id="edit_agent_id" name="agent_id" required>
                            {% for agent in agents %}
                            <option value="{{ agent.Agent_Id }}">{{ agent.Agent_Name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_num_tkts" class="form-label">Number of TKTS:</label>
                        <input type="number" class="form-control" id="edit_num_tkts" name="num_tkts" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEditBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this transaction? This action cannot be undone.</p>
                <input type="hidden" id="delete_id">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Table elements
        const table = document.getElementById('transactionsTable');
        const recordCount = document.getElementById('recordCount');
        const sortHeaders = document.querySelectorAll('th[data-sort]');
        const searchInput = document.getElementById('searchInput');
        const searchButton = document.getElementById('searchButton');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        const filterDateBtn = document.getElementById('filterDateBtn');
        const exportBtn = document.getElementById('exportBtn');
        const printBtn = document.getElementById('printBtn');
        
        // Dashboard elements
        const totalTktsElement = document.getElementById('totalTkts');
        const uniqueAgentsElement = document.getElementById('uniqueAgents');
        const avgTktsElement = document.getElementById('avgTkts');
        const agentChartCtx = document.getElementById('agentChart')?.getContext('2d');
        const trendChartCtx = document.getElementById('trendChart')?.getContext('2d');
        
        let agentChart = null;
        let trendChart = null;
        let currentSort = { column: 'date', direction: 'desc' };
        
        // Find the latest transaction date
        let latestDate = new Date().toISOString().split('T')[0]; // Default to today
        if (table) {
            const dateColumn = 1; // Date is in the second column (index 1)
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 0) {
                // Get all dates from the table
                const dates = Array.from(rows).map(row => new Date(row.cells[dateColumn].textContent));
                // Find the latest date
                if (dates.length > 0) {
                    const maxDate = new Date(Math.max.apply(null, dates));
                    latestDate = maxDate.toISOString().split('T')[0];
                }
            }
        }
        
        // Set date inputs - both start and end date to latest transaction date
        if (startDateInput) startDateInput.value = latestDate;
        if (endDateInput) endDateInput.value = latestDate;
        
        // Set date input for new transaction to today
        const dateInput = document.getElementById('date');
        if (dateInput) dateInput.value = new Date().toISOString().split('T')[0];
        
        // Search and filter functionality
        function filterTable() {
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const startDate = startDateInput && startDateInput.value ? new Date(startDateInput.value) : null;
            const endDate = endDateInput && endDateInput.value ? new Date(endDateInput.value) : null;
            
            if (!table) return;
            
            const rows = table.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const dateCell = row.cells[1].textContent;
                const rowDate = new Date(dateCell);
                
                let dateMatch = true;
                if (startDate && endDate) {
                    // Set end date to end of day for inclusive comparison
                    const endOfDay = new Date(endDate);
                    endOfDay.setHours(23, 59, 59, 999);
                    dateMatch = rowDate >= startDate && rowDate <= endOfDay;
                }
                
                if (text.includes(searchTerm) && dateMatch) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            if (recordCount) {
                recordCount.textContent = visibleCount;
            }
            
            // Update dashboard with filtered data
            updateDashboard();
        }
        
        if (searchInput) {
            searchInput.addEventListener('keyup', filterTable);
        }
        if (searchButton) {
            searchButton.addEventListener('click', filterTable);
        }
        if (filterDateBtn) {
            filterDateBtn.addEventListener('click', filterTable);
        }
        
        // Date input change events to trigger dashboard update
        if (startDateInput) {
            startDateInput.addEventListener('change', function() {
                if (filterDateBtn) {
                    filterDateBtn.click(); // Automatically apply filter when date changes
                } else {
                    filterTable(); // If no button, just filter directly
                }
            });
        }
        
        if (endDateInput) {
            endDateInput.addEventListener('change', function() {
                if (filterDateBtn) {
                    filterDateBtn.click(); // Automatically apply filter when date changes
                } else {
                    filterTable(); // If no button, just filter directly
                }
            });
        }
        
        // Sorting functionality
        function sortTable(column) {
            if (!table) return;
            
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            const direction = currentSort.column === column && currentSort.direction === 'asc' ? 'desc' : 'asc';
            
            rows.sort((a, b) => {
                let valueA, valueB;
                
                if (column === 'id') {
                    valueA = parseInt(a.cells[0].textContent);
                    valueB = parseInt(b.cells[0].textContent);
                } else if (column === 'date') {
                    valueA = new Date(a.cells[1].textContent);
                    valueB = new Date(b.cells[1].textContent);
                } else if (column === 'agent') {
                    valueA = a.cells[2].textContent.toLowerCase();
                    valueB = b.cells[2].textContent.toLowerCase();
                } else if (column === 'tkts') {
                    valueA = parseInt(a.cells[3].textContent);
                    valueB = parseInt(b.cells[3].textContent);
                }
                
                if (direction === 'asc') {
                    return valueA > valueB ? 1 : -1;
                } else {
                    return valueA < valueB ? 1 : -1;
                }
            });
            
            // Update table with sorted rows
            const tbody = table.querySelector('tbody');
            rows.forEach(row => tbody.appendChild(row));
            
            // Update current sort
            currentSort = { column, direction };
            
            // Update sort indicators
            sortHeaders.forEach(header => {
                const headerColumn = header.getAttribute('data-sort');
                const icon = header.querySelector('.sort-icon');
                
                if (headerColumn === column) {
                    icon.className = `bi ${direction === 'asc' ? 'bi-arrow-up' : 'bi-arrow-down'} sort-icon`;
                } else {
                    icon.className = 'bi bi-arrow-down-up sort-icon';
                }
            });
            
            // Update dashboard after sorting
            updateDashboard();
        }
        
        if (sortHeaders) {
            sortHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-sort');
                    sortTable(column);
                });
            });
        }
        
        // Print functionality
        if (printBtn) {
            printBtn.addEventListener('click', function() {
                window.print();
            });
        }
        
        // Export to Excel functionality
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                if (!table) return;
                
                // Get visible rows only
                const visibleRows = Array.from(table.querySelectorAll('tbody tr'))
                    .filter(row => row.style.display !== 'none');
                
                // Create worksheet data
                const wsData = [
                    ['ID', 'Date', 'Employee', 'TKTS'] // Header row
                ];
                
                visibleRows.forEach(row => {
                    const rowData = [];
                    Array.from(row.cells).forEach((cell, index) => {
                        // Skip the actions column
                        if (index < 4) {
                            rowData.push(cell.textContent);
                        }
                    });
                    wsData.push(rowData);
                });
                
                // Create workbook and worksheet
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(wsData);
                
                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'Employee Transactions');
                
                // Generate filename with current date
                const now = new Date();
                const filename = `Employee_Transactions_${now.toISOString().split('T')[0]}.xlsx`;
                
                // Export to file
                XLSX.writeFile(wb, filename);
            });
        }
        
        // Update dashboard
        function updateDashboard() {
            if (!table) return;
            
            // Get visible rows only (filtered by date and search)
            const visibleRows = Array.from(table.querySelectorAll('tbody tr'))
                .filter(row => row.style.display !== 'none');
            
            // Calculate metrics
            let totalTkts = 0;
            const agents = new Set();
            const dateMap = new Map(); // For trend chart
            const agentMap = new Map(); // For agent chart
            
            visibleRows.forEach(row => {
                const tkts = parseInt(row.cells[3].textContent);
                const agent = row.cells[2].textContent;
                const dateStr = row.cells[1].textContent;
                
                totalTkts += tkts;
                agents.add(agent);
                
                // Aggregate by agent
                if (agentMap.has(agent)) {
                    agentMap.set(agent, agentMap.get(agent) + tkts);
                } else {
                    agentMap.set(agent, tkts);
                }
                
                // Aggregate by date
                if (dateMap.has(dateStr)) {
                    dateMap.set(dateStr, dateMap.get(dateStr) + tkts);
                } else {
                    dateMap.set(dateStr, tkts);
                }
            });
            
            // Update summary cards
            if (totalTktsElement) totalTktsElement.textContent = totalTkts;
            if (uniqueAgentsElement) uniqueAgentsElement.textContent = agents.size;
            
            // Calculate average TKTS per day
            const avgTkts = dateMap.size > 0 ? (totalTkts / dateMap.size).toFixed(2) : '0';
            if (avgTktsElement) avgTktsElement.textContent = avgTkts;
            
            // Update charts
            updateCharts(agentMap, dateMap);
        }
        
        // Update charts
        function updateCharts(agentMap, dateMap) {
            // Destroy existing charts to prevent duplicates
            if (agentChart) agentChart.destroy();
            if (trendChart) trendChart.destroy();
            
            // Agent chart
            if (agentChartCtx) {
                const agentLabels = Array.from(agentMap.keys());
                const agentValues = Array.from(agentMap.values());
                
                // Generate colors
                const backgroundColors = agentLabels.map((_, i) => {
                    const hue = (i * 137) % 360; // Golden angle approximation for good distribution
                    return `hsl(${hue}, 70%, 60%)`;
                });
                
                agentChart = new Chart(agentChartCtx, {
                    type: 'pie',
                    data: {
                        labels: agentLabels,
                        datasets: [{
                            data: agentValues,
                            backgroundColor: backgroundColors
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'right',
                            },
                            title: {
                                display: true,
                                text: 'Employee Distribution'
                            }
                        }
                    }
                });
            }
            
            // Trend chart
            if (trendChartCtx) {
                // Sort dates chronologically
                const sortedDates = Array.from(dateMap.keys()).sort((a, b) => new Date(a) - new Date(b));
                const dateValues = sortedDates.map(date => dateMap.get(date));
                
                trendChart = new Chart(trendChartCtx, {
                    type: 'line',
                    data: {
                        labels: sortedDates,
                        datasets: [{
                            label: 'TKTS',
                            data: dateValues,
                            borderColor: '#36A2EB',
                            fill: false
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            },
                            title: {
                                display: true,
                                text: 'TKTS Trend'
                            }
                        }
                    }
                });
            }
        }
        
        // Edit transaction
        const editModal = document.getElementById('editModal') ? new bootstrap.Modal(document.getElementById('editModal')) : null;
        const editButtons = document.querySelectorAll('.edit-btn');
        const saveEditBtn = document.getElementById('saveEditBtn');

        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const date = this.getAttribute('data-date');
                const agentId = this.getAttribute('data-agent-id');
                const tkts = this.getAttribute('data-tkts');
                
                document.getElementById('edit_id').value = id;
                document.getElementById('edit_date').value = date;
                document.getElementById('edit_agent_id').value = agentId;
                document.getElementById('edit_num_tkts').value = tkts;
                
                if (editModal) {
                    editModal.show();
                }
            });
        });

        if (saveEditBtn) {
            saveEditBtn.addEventListener('click', function() {
                const form = document.getElementById('editForm');
                if (!form) return;
                
                form.submit();
            });
        }

        // Delete transaction
        const deleteButtons = document.querySelectorAll('.delete-btn');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                
                if (confirm('Are you sure you want to delete this transaction?')) {
                    fetch(`/employees/delete/${id}`, {
                        method: 'DELETE'
                    })
                    .then(response => {
                        if (response.ok) {
                            // Refresh the page to show updated data
                            window.location.reload();
                        } else {
                            return response.json().then(data => {
                                throw new Error(data.detail || 'Error deleting transaction');
                            });
                        }
                    })
                    .catch(error => {
                        alert('Error: ' + error.message);
                    });
                }
            });
        });
        
        // Update grid totals specifically for this page
        function updateGridTotals() {
            const table = document.getElementById('transactionsTable');
            if (!table) return;
            
            const rows = Array.from(table.querySelectorAll('tbody tr'))
                .filter(row => row.style.display !== 'none');
            
            // Calculate total TKTs
            let totalTkts = 0;
            rows.forEach(row => {
                if (row.cells.length > 3) {
                    const tktsValue = row.cells[3].textContent.trim();
                    if (tktsValue && !isNaN(tktsValue)) {
                        totalTkts += parseInt(tktsValue);
                    }
                }
            });
            
            // Update grid total element
            const gridTotalTkts = document.getElementById('gridTotalTkts');
            if (gridTotalTkts) {
                gridTotalTkts.textContent = totalTkts;
            }
        }
        
        // Call updateGridTotals after filtering
        const originalFilterTable = filterTable;
        filterTable = function() {
            originalFilterTable();
            updateGridTotals();
        };
        
        // Initial update of grid totals
        setTimeout(updateGridTotals, 500);
        
        // Initial filter to update dashboard based on default date range
        setTimeout(filterTable, 100);
    });
</script>
{% endblock %}











