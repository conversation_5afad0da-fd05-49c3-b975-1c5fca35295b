{% extends "base.html" %}

{% block title %}Home - Comsys Software PMO & CSO System{% endblock %}

{% block content %}
<!-- Logo Section with Background -->
<div class="py-5 mb-4" style="background: linear-gradient(to right, #1b263b, #0d1b2a); color: white;">
    <div class="container text-center">
        <h2 class="mb-4">Welcome to Comsys Software PMO & CSO System</h2>
        <img src="{{ image_path }}" alt="Home Image" class="img-fluid rounded shadow" style="max-width: 70%; height: auto;">
    </div>
</div>

<!-- Dashboard Section -->
<div class="card mb-4 shadow-lg border-0">
    <div class="card-header text-white" style="background: linear-gradient(to right, #1b263b, #0d1b2a);">
        <h4 class="mb-0"><i class="bi bi-graph-up-arrow me-2"></i>Current Month Dashboard</h4>
    </div>
    <div class="card-body">
        <!-- Summary Cards -->
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="card bg-light h-100 shadow-sm">
                    <div class="card-body text-center">
                        <h5 class="card-title text-muted">Total TKTS</h5>
                        <h2 class="display-4 text-primary" id="totalTkts">{{ total_tkts }}</h2>
                    </div>
                </div>
            </div>

            <div class="col-md-6 mb-3">
                <div class="card bg-light h-100 shadow-sm">
                    <div class="card-body text-center">
                        <h5 class="card-title text-muted">Avg TKTS/Day</h5>
                        <h2 class="display-4 text-primary" id="avgTkts">{{ avg_tkts_per_day }}</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <strong><i class="bi bi-bar-chart-line me-1"></i>Tickets Trend</strong>
                    </div>
                <div class="card-body" style="height: 400px;">
                    <canvas id="trendChart" class="w-100 h-100"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    const trendData = {
        labels: {{ trend_labels | safe }},
        datasets: [{
            label: 'Tickets',
            data: {{ trend_values | safe }},
            borderColor: '#007bff',
            backgroundColor: 'rgba(0,123,255,0.1)',
            fill: true,
            tension: 0.3,
        }]
    };

    new Chart(document.getElementById('trendChart'), {
        type: 'line',
        data: trendData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    labels: {
                        color: '#000'
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: '#333'
                    }
                },
                y: {
                    ticks: {
                        color: '#333'
                    }
                }
            }
        }
    });
</script>
{% endblock %}
