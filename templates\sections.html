{% extends "base.html" %}

{% block title %}Section TKTS Form - Comsys Software PMO & CSO System{% endblock %}

{% block content %}
<!-- Form Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h2>Add New Section TKTs</h2>
    </div>
    <div class="card-body">
        <form action="/sections/submit" method="post">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="date" name="date" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="section_id" class="form-label">Section</label>
                    <select class="form-select" id="section_id" name="section_id" required>
                        <option value="" selected disabled>Select Section</option>
                        {% for section in sections %}
                        <option value="{{ section.SectionID }}">{{ section.Section }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="num_tkts" class="form-label">Number of TKTs</label>
                    <input type="number" class="form-control" id="num_tkts" name="num_tkts" min="0" required>
                </div>
            </div>
            <div class="text-end">
                <button type="submit" class="btn btn-primary" {% if not current_user.CanAdd %}disabled{% endif %}>
                    <i class="bi bi-plus-circle me-2"></i>Add Transaction
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Filter Card -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h2>Filter & Export</h2>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Search Filter -->
            <div class="col-md-4 mb-3">
                <div class="card">
                    <div class="card-header">Search Filter</div>
                    <div class="card-body">
                        <label for="searchInput" class="form-label">Search:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchInput" placeholder="Search...">
                            <button class="btn btn-primary" type="button" id="searchButton">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Date Filter -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">Date Range Filter</div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-5">
                                <label for="startDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-5">
                                <label for="endDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button class="btn btn-primary w-100" id="filterDateBtn">
                                    <i class="bi bi-funnel me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Section Filter -->
            <div class="col-md-2 mb-3">
                <div class="card h-100">
                    <div class="card-header">Section</div>
                    <div class="card-body d-flex flex-column">
                        <label for="sectionFilter" class="form-label">Filter by:</label>
                        <select class="form-select mb-auto" id="sectionFilter">
                            <option value="">All Sections</option>
                            {% for section in sections %}
                            <option value="{{ section.Section|lower }}">{{ section.Section }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 text-end">
                <button class="btn btn-secondary" id="resetFilterBtn">
                    <i class="bi bi-arrow-counterclockwise me-2"></i>Reset Filters
                </button>
                <button class="btn btn-success" id="exportBtn">
                    <i class="bi bi-file-excel me-2"></i>Export to Excel
                </button>
                <button class="btn btn-info" id="printBtn">
                    <i class="bi bi-printer me-2"></i>Print
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Grid -->
<div class="card">
    <div class="card-header bg-secondary text-white">
        <h2>Transactions History</h2>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="transactionsTable">
                <thead>
                    <tr>
                        <th data-sort="id">ID <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="date">Date <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="section">Section <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="tkts">TKTS <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.ID }}</td>
                        <td>{{ transaction.Date.strftime('%Y-%m-%d') if transaction.Date is not string else transaction.Date }}</td>
                        <td>{{ transaction.Section }}</td>
                        <td>{{ transaction.Num_TKTS }}</td>
                        <td>
                            <button class="btn btn-sm btn-warning edit-btn" data-id="{{ transaction.ID }}" 
                                    data-date="{{ transaction.Date.strftime('%Y-%m-%d') if transaction.Date is not string else transaction.Date }}" 
                                    data-section="{{ transaction.Section }}"
                                    data-section-id="{{ transaction.Sec_ID }}"
                                    data-tkts="{{ transaction.Num_TKTS }}"
                                    {% if not current_user.CanEdit %}disabled{% endif %}>
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-btn" data-id="{{ transaction.ID }}"
                                    {% if not current_user.CanDelete %}disabled{% endif %}>
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-secondary fw-bold">
                        <td colspan="3" class="text-end">Total:</td>
                        <td id="gridTotalTkts">0</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="card-footer bg-light">
        <strong>Total Records: <span id="recordCount">{{ transactions|length }}</span></strong>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editModalLabel">Edit Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" action="/sections/update" method="post">
                    <input type="hidden" id="edit_id" name="id">
                    <div class="mb-3">
                        <label for="edit_date" class="form-label">Date:</label>
                        <input type="date" class="form-control" id="edit_date" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_section_id" class="form-label">Section:</label>
                        <select class="form-select" id="edit_section_id" name="section_id" required>
                            {% for section in sections %}
                            <option value="{{ section.SectionID }}">{{ section.Section }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_num_tkts" class="form-label">Number of TKTs:</label>
                        <input type="number" class="form-control" id="edit_num_tkts" name="num_tkts" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEditBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Delete Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this transaction? This action cannot be undone.</p>
                <form id="deleteForm" action="/sections/delete" method="post">
                    <input type="hidden" id="delete_id" name="id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const searchButton = document.getElementById('searchButton');
        const table = document.getElementById('transactionsTable');
        const recordCount = document.getElementById('recordCount');
        const sortHeaders = document.querySelectorAll('th[data-sort]');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        const filterDateBtn = document.getElementById('filterDateBtn');
        const sectionFilter = document.getElementById('sectionFilter');
        const resetFilterBtn = document.getElementById('resetFilterBtn');
        const printBtn = document.getElementById('printBtn');
        const exportBtn = document.getElementById('exportBtn');
        
        let currentSort = { column: 'date', direction: 'desc' };
        
        // Find the latest transaction date
        let latestDate = new Date().toISOString().split('T')[0]; // Default to today
        if (table) {
            const dateColumn = 1; // Date is in the second column (index 1)
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 0) {
                // Get all dates from the table
                const dates = Array.from(rows).map(row => new Date(row.cells[dateColumn].textContent));
                // Find the latest date
                if (dates.length > 0) {
                    const maxDate = new Date(Math.max.apply(null, dates));
                    latestDate = maxDate.toISOString().split('T')[0];
                }
            }
        }
        
        // Set date inputs - both start and end date to latest transaction date
        if (startDateInput) {
            startDateInput.value = latestDate;
        }
        
        if (endDateInput) {
            endDateInput.value = latestDate;
        }
        
        // Set default date for new transaction to today
        const dateInput = document.getElementById('date');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
        
        // Search functionality
        function filterTable() {
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const startDate = startDateInput && startDateInput.value ? new Date(startDateInput.value) : null;
            const endDate = endDateInput && endDateInput.value ? new Date(endDateInput.value) : null;
            const selectedSection = sectionFilter ? sectionFilter.value.toLowerCase() : '';
            
            if (!table) return;
            
            const rows = table.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const dateCell = row.cells[1].textContent;
                const sectionCell = row.cells[2].textContent.toLowerCase();
                const rowDate = new Date(dateCell);
                
                let showRow = true;
                
                // Apply search filter
                if (searchTerm) {
                    showRow = false;
                    for (let i = 0; i < row.cells.length - 1; i++) {
                        if (row.cells[i].textContent.toLowerCase().includes(searchTerm)) {
                            showRow = true;
                            break;
                        }
                    }
                }
                
                // Apply date filter
                if (showRow && startDate && endDate) {
                    // Set end date to end of day for inclusive comparison
                    const endOfDay = new Date(endDate);
                    endOfDay.setHours(23, 59, 59, 999);
                    showRow = rowDate >= startDate && rowDate <= endOfDay;
                }
                
                // Apply section filter
                if (showRow && selectedSection) {
                    showRow = sectionCell === selectedSection;
                }
                
                row.style.display = showRow ? '' : 'none';
                if (showRow) visibleCount++;
            });
            
            if (recordCount) {
                recordCount.textContent = visibleCount;
            }
            
            // Update dashboard with filtered data
            updateDashboard();
        }
        
        // Update dashboard based on visible rows
        function updateDashboard() {
            if (!table) return;
            
            const rows = Array.from(table.querySelectorAll('tbody tr'))
                .filter(row => row.style.display !== 'none');
            
            // Calculate total TKTs
            let totalTkts = 0;
            rows.forEach(row => {
                totalTkts += parseInt(row.cells[3].textContent);
            });
            
            // Get unique dates
            const uniqueDates = new Set();
            rows.forEach(row => {
                uniqueDates.add(row.cells[1].textContent);
            });
            
            // Update dashboard elements
            const totalTktsElement = document.getElementById('totalTkts');
            const avgTktsElement = document.getElementById('avgTkts');
            
            if (totalTktsElement) {
                totalTktsElement.textContent = totalTkts;
            }
            
            if (avgTktsElement && uniqueDates.size > 0) {
                avgTktsElement.textContent = Math.round(totalTkts / uniqueDates.size);
            } else if (avgTktsElement) {
                avgTktsElement.textContent = '0';
            }
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', filterTable);
        }
        
        if (searchInput) {
            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    filterTable();
                }
            });
        }
        
        if (filterDateBtn) {
            filterDateBtn.addEventListener('click', filterTable);
        }
        
        if (sectionFilter) {
            sectionFilter.addEventListener('change', filterTable);
        }
        
        if (resetFilterBtn) {
            resetFilterBtn.addEventListener('click', function() {
                if (searchInput) searchInput.value = '';
                if (startDateInput) startDateInput.value = latestDate;
                if (endDateInput) endDateInput.value = latestDate;
                if (sectionFilter) sectionFilter.value = '';
                filterTable();
            });
        }
        
        // Sorting functionality
        if (sortHeaders) {
            sortHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-sort');
                    sortTable(column);
                });
            });
        }
        
        function sortTable(column) {
            if (!table) return;
            
            const direction = currentSort.column === column && currentSort.direction === 'asc' ? 'desc' : 'asc';
            const rows = Array.from(table.querySelectorAll('tbody tr'));
            
            rows.sort((a, b) => {
                let aValue, bValue;
                
                switch(column) {
                    case 'id':
                        aValue = parseInt(a.cells[0].textContent);
                        bValue = parseInt(b.cells[0].textContent);
                        break;
                    case 'date':
                        aValue = new Date(a.cells[1].textContent);
                        bValue = new Date(b.cells[1].textContent);
                        break;
                    case 'section':
                        aValue = a.cells[2].textContent.toLowerCase();
                        bValue = b.cells[2].textContent.toLowerCase();
                        break;
                    case 'tkts':
                        aValue = parseInt(a.cells[3].textContent);
                        bValue = parseInt(b.cells[3].textContent);
                        break;
                    default:
                        return 0;
                }
                
                if (direction === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });
            
            // Update table with sorted rows
            const tbody = table.querySelector('tbody');
            rows.forEach(row => tbody.appendChild(row));
            
            // Update sort indicators
            sortHeaders.forEach(header => {
                const headerColumn = header.getAttribute('data-sort');
                const icon = header.querySelector('.sort-icon');
                
                if (headerColumn === column) {
                    icon.className = 'bi sort-icon ' + (direction === 'asc' ? 'bi-arrow-up' : 'bi-arrow-down');
                } else {
                    icon.className = 'bi bi-arrow-down-up sort-icon';
                }
            });
            
            currentSort = { column, direction };
        }
        
        // Initial sort by date (descending)
        setTimeout(() => {
            const dateHeader = document.querySelector('th[data-sort="date"]');
            if (dateHeader) {
                dateHeader.click();
                dateHeader.click(); // Click twice to ensure descending order
            }
        }, 100);
        
        // Print functionality
        if (printBtn) {
            printBtn.addEventListener('click', function() {
                window.print();
            });
        }
        
        // Export to Excel functionality
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                if (!table) return;
                
                // Get visible rows only
                const visibleRows = Array.from(table.querySelectorAll('tbody tr'))
                    .filter(row => row.style.display !== 'none');
                
                // Create worksheet data
                const wsData = [
                    ['ID', 'Date', 'Section', 'TKTS'] // Header row
                ];
                
                visibleRows.forEach(row => {
                    const rowData = [];
                    // Skip the actions column
                    for (let i = 0; i < 4; i++) {
                        rowData.push(row.cells[i].textContent);
                    }
                    wsData.push(rowData);
                });
                
                // Create workbook and worksheet
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(wsData);
                
                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'Section Transactions');
                
                // Generate filename with current date
                const now = new Date();
                const filename = `Section_Transactions_${now.toISOString().split('T')[0]}.xlsx`;
                
                // Export to file
                XLSX.writeFile(wb, filename);
            });
        }
        
        // Edit transaction
        const editModal = document.getElementById('editModal');
        const editBtns = document.querySelectorAll('.edit-btn');
        const editForm = document.getElementById('editForm');
        const saveEditBtn = document.getElementById('saveEditBtn');
        
        if (editBtns) {
            editBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const date = this.getAttribute('data-date');
                    const sectionId = this.getAttribute('data-section-id');
                    const tkts = this.getAttribute('data-tkts');
                    
                    document.getElementById('edit_id').value = id;
                    document.getElementById('edit_date').value = date;
                    document.getElementById('edit_section_id').value = sectionId;
                    document.getElementById('edit_num_tkts').value = tkts;
                    
                    const modal = new bootstrap.Modal(editModal);
                    modal.show();
                });
            });
        }
        
        if (saveEditBtn) {
            saveEditBtn.addEventListener('click', function() {
                editForm.submit();
            });
        }
        
        // Delete transaction
        const deleteModal = document.getElementById('deleteModal');
        const deleteBtns = document.querySelectorAll('.delete-btn');
        const deleteForm = document.getElementById('deleteForm');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        
        if (deleteBtns) {
            deleteBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    document.getElementById('delete_id').value = id;
                    
                    const modal = new bootstrap.Modal(deleteModal);
                    modal.show();
                });
            });
        }
        
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', function() {
                deleteForm.submit();
            });
        }
        
        // Update grid totals specifically for this page
        function updateGridTotals() {
            const table = document.getElementById('transactionsTable');
            if (!table) return;
            
            const rows = Array.from(table.querySelectorAll('tbody tr'))
                .filter(row => row.style.display !== 'none');
            
            // Calculate total TKTs
            let totalTkts = 0;
            rows.forEach(row => {
                if (row.cells.length > 3) {
                    const tktsValue = row.cells[3].textContent.trim();
                    if (tktsValue && !isNaN(tktsValue)) {
                        totalTkts += parseInt(tktsValue);
                    }
                }
            });
            
            // Update grid total element
            const gridTotalTkts = document.getElementById('gridTotalTkts');
            if (gridTotalTkts) {
                gridTotalTkts.textContent = totalTkts;
            }
        }
        
        // Call updateGridTotals after filtering
        const originalFilterTable = filterTable;
        filterTable = function() {
            originalFilterTable();
            updateGridTotals();
        };
        
        // Initial update of grid totals
        setTimeout(updateGridTotals, 500);
        
        // Initial filter to update dashboard
        setTimeout(filterTable, 100);
    });
</script>
{% endblock %}









