document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const searchButton = document.getElementById('searchButton');
    const table = document.getElementById('transactionsTable');
    const recordCount = document.getElementById('recordCount');
    const sortHeaders = document.querySelectorAll('th[data-sort]');
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    const filterDateBtn = document.getElementById('filterDateBtn');
    const printBtn = document.getElementById('printBtn');
    const exportBtn = document.getElementById('exportBtn');
    
    // Dashboard elements
    const totalTktsElement = document.getElementById('totalTkts');
    const uniqueChannelsElement = document.getElementById('uniqueChannels');
    const avgTktsElement = document.getElementById('avgTkts');
    const channelChartCtx = document.getElementById('channelChart')?.getContext('2d');
    const trendChartCtx = document.getElementById('trendChart')?.getContext('2d');
    
    let channelChart = null;
    let trendChart = null;
    
    // Find the latest transaction date
    let latestDate = new Date().toISOString().split('T')[0]; // Default to today
    if (table) {
        const dateColumn = 1; // Date is in the second column (index 1)
        const rows = table.querySelectorAll('tbody tr');
        if (rows.length > 0) {
            // Get all dates from the table
            const dates = Array.from(rows).map(row => new Date(row.cells[dateColumn].textContent));
            // Find the latest date
            if (dates.length > 0) {
                const maxDate = new Date(Math.max.apply(null, dates));
                latestDate = maxDate.toISOString().split('T')[0];
            }
        }
    }
    
    // Set date inputs - both start and end date to latest transaction date
    if (startDateInput) {
        startDateInput.value = latestDate;
    }
    
    // Set end date to latest transaction date
    if (endDateInput) {
        endDateInput.value = latestDate;
    }
    
    // Set default date for new transaction to today
    const dateInput = document.getElementById('date');
    if (dateInput) {
        dateInput.value = new Date().toISOString().split('T')[0];
    }
    
    // Search functionality
    function filterTable() {
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const startDate = startDateInput && startDateInput.value ? new Date(startDateInput.value) : null;
        const endDate = endDateInput && endDateInput.value ? new Date(endDateInput.value) : null;
        
        if (!table) return;
        
        const rows = table.querySelectorAll('tbody tr');
        let visibleCount = 0;
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const dateCell = row.cells[1].textContent;
            const rowDate = new Date(dateCell);
            
            let dateMatch = true;
            if (startDate && endDate) {
                // Set end date to end of day for inclusive comparison
                const endOfDay = new Date(endDate);
                endOfDay.setHours(23, 59, 59, 999);
                dateMatch = rowDate >= startDate && rowDate <= endOfDay;
            }
            
            if (text.includes(searchTerm) && dateMatch) {
                row.style.display = '';
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        if (recordCount) {
            recordCount.textContent = visibleCount;
        }
        updateDashboard();
    }
    
    if (searchInput) {
        searchInput.addEventListener('keyup', filterTable);
    }
    if (searchButton) {
        searchButton.addEventListener('click', filterTable);
    }
    if (filterDateBtn) {
        filterDateBtn.addEventListener('click', filterTable);
    }
    
    // Initial filter and dashboard update
    if (filterDateBtn) {
        // Trigger initial filter
        setTimeout(filterTable, 500);
    }
    
    // Sorting functionality
    let currentSort = {
        column: 'date',
        direction: 'desc'
    };
    
    function sortTable(column) {
        if (!table) return;
        
        const rows = Array.from(table.querySelectorAll('tbody tr'));
        const direction = column === currentSort.column && currentSort.direction === 'asc' ? 'desc' : 'asc';
        
        rows.sort((a, b) => {
            const aValue = a.cells[getColumnIndex(column)].textContent;
            const bValue = b.cells[getColumnIndex(column)].textContent;
            
            if (column === 'id' || column === 'tkts') {
                return direction === 'asc' 
                    ? parseInt(aValue) - parseInt(bValue)
                    : parseInt(bValue) - parseInt(aValue);
            } else if (column === 'date') {
                return direction === 'asc' 
                    ? new Date(aValue) - new Date(bValue)
                    : new Date(bValue) - new Date(aValue);
            } else {
                return direction === 'asc'
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            }
        });
        
        // Update the table with sorted rows
        const tbody = table.querySelector('tbody');
        rows.forEach(row => tbody.appendChild(row));
        
        // Update sort indicators
        if (sortHeaders) {
            sortHeaders.forEach(header => {
                const headerColumn = header.getAttribute('data-sort');
                const icon = header.querySelector('.sort-icon');
                
                if (headerColumn === column) {
                    icon.className = direction === 'asc' 
                        ? 'bi bi-arrow-up sort-icon active' 
                        : 'bi bi-arrow-down sort-icon active';
                } else {
                    icon.className = 'bi bi-arrow-down-up sort-icon';
                }
            });
        }
        
        currentSort = { column, direction };
    }
    
    function getColumnIndex(columnName) {
        switch(columnName) {
            case 'id': return 0;
            case 'date': return 1;
            case 'channel': return 2;
            case 'tkts': return 3;
            default: return 0;
        }
    }
    
    if (sortHeaders) {
        sortHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const column = header.getAttribute('data-sort');
                sortTable(column);
            });
        });
    }
    
    // Print functionality
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            window.print();
        });
    }
    
    // Export to Excel functionality
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            if (!table) return;
            
            // Get visible rows only
            const visibleRows = Array.from(table.querySelectorAll('tbody tr'))
                .filter(row => row.style.display !== 'none');
            
            // Create worksheet data
            const wsData = [
                ['ID', 'Date', 'Channel', 'TKTS'] // Header row
            ];
            
            visibleRows.forEach(row => {
                const rowData = [];
                Array.from(row.cells).forEach((cell, index) => {
                    // Skip the actions column
                    if (index < 4) {
                        rowData.push(cell.textContent);
                    }
                });
                wsData.push(rowData);
            });
            
            // Create workbook and worksheet
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(wsData);
            
            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Transactions');
            
            // Generate filename with current date
            const now = new Date();
            const filename = `Transactions_${now.toISOString().split('T')[0]}.xlsx`;
            
            // Export to file
            XLSX.writeFile(wb, filename);
        });
    }
    
    // Dashboard functionality
    function updateDashboard() {
        if (!table) return;
        
        const visibleRows = Array.from(table.querySelectorAll('tbody tr'))
            .filter(row => row.style.display !== 'none');
        
        // Calculate metrics
        let totalTkts = 0;
        const channels = new Set();
        const dateMap = new Map(); // For trend chart
        const channelMap = new Map(); // For channel chart
        
        visibleRows.forEach(row => {
            const tkts = parseInt(row.cells[3].textContent);
            const channel = row.cells[2].textContent;
            const dateStr = row.cells[1].textContent;
            
            totalTkts += tkts;
            channels.add(channel);
            
            // Aggregate by channel
            if (channelMap.has(channel)) {
                channelMap.set(channel, channelMap.get(channel) + tkts);
            } else {
                channelMap.set(channel, tkts);
            }
            
            // Aggregate by date (for trend)
            if (dateMap.has(dateStr)) {
                dateMap.set(dateStr, dateMap.get(dateStr) + tkts);
            } else {
                dateMap.set(dateStr, tkts);
            }
        });
        
        // Update summary cards
        if (totalTktsElement) {
            totalTktsElement.textContent = totalTkts;
        }
        if (uniqueChannelsElement) {
            uniqueChannelsElement.textContent = channels.size;
        }
        
        // Calculate average TKTS per day
        const avgTkts = dateMap.size > 0 ? Math.round(totalTkts / dateMap.size) : 0;
        if (avgTktsElement) {
            avgTktsElement.textContent = avgTkts;
        }
        
        // Update channel chart
        if (channelChartCtx) {
            updateChannelChart(channelMap);
        }
        
        // Update trend chart
        if (trendChartCtx) {
            updateTrendChart(dateMap);
        }
    }
    
    function updateChannelChart(channelMap) {
        if (!channelChartCtx) return;
        
        // Convert map to arrays for Chart.js
        const labels = Array.from(channelMap.keys());
        const data = Array.from(channelMap.values());
        
        // Generate colors
        const backgroundColors = labels.map((_, i) => 
            `hsl(${(i * 360 / labels.length) % 360}, 70%, 60%)`
        );
        
        // Destroy previous chart if exists
        if (channelChart) {
            channelChart.destroy();
        }
        
        // Create new chart
        channelChart = new Chart(channelChartCtx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 15,
                            font: {
                                size: 11
                            }
                        }
                    },
                    title: {
                        display: true,
                        text: 'TKTS by Channel'
                    }
                }
            }
        });
    }
    
    function updateTrendChart(dateMap) {
        if (!trendChartCtx) return;
        
        // Sort dates
        const sortedDates = Array.from(dateMap.keys()).sort();
        const data = sortedDates.map(date => dateMap.get(date));
        
        // Destroy previous chart if exists
        if (trendChart) {
            trendChart.destroy();
        }
        
        // Create new chart
        trendChart = new Chart(trendChartCtx, {
            type: 'line',
            data: {
                labels: sortedDates,
                datasets: [{
                    label: 'TKTS',
                    data: data,
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of TKTS'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    }
                }
            }
        });
    }
    
    // Initial sort by date (newest first)
    if (table) {
        sortTable('date');
    }
    
    // Initial filter and dashboard update
    filterTable();

    // Edit transaction
    const editModal = document.getElementById('editModal') ? new bootstrap.Modal(document.getElementById('editModal')) : null;
    const editButtons = document.querySelectorAll('.edit-btn');
    const saveEditBtn = document.getElementById('saveEditBtn');

    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const date = this.getAttribute('data-date');
            const channelId = this.getAttribute('data-channel-id');
            const tkts = this.getAttribute('data-tkts');
            
            document.getElementById('edit_id').value = id;
            document.getElementById('edit_date').value = date;
            document.getElementById('edit_channel_id').value = channelId;
            document.getElementById('edit_num_tkts').value = tkts;
            
            if (editModal) {
                editModal.show();
            }
        });
    });

    if (saveEditBtn) {
        saveEditBtn.addEventListener('click', function() {
            const form = document.getElementById('editForm');
            if (!form) return;
            
            const formData = new FormData(form);
            
            fetch('/update', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    // Refresh the page to show updated data
                    window.location.reload();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.detail || 'Error updating transaction');
                    });
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        });
    }

    // Delete transaction
    const deleteModal = document.getElementById('deleteModal') ? new bootstrap.Modal(document.getElementById('deleteModal')) : null;
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            if (deleteModal) {
                document.getElementById('delete_id').value = id;
                deleteModal.show();
            }
        });
    });

    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const id = document.getElementById('delete_id').value;
            
            fetch(`/delete/${id}`, {
                method: 'DELETE'
            })
            .then(response => {
                if (response.ok) {
                    // Refresh the page to show updated data
                    window.location.reload();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.detail || 'Error deleting transaction');
                    });
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        });
    }
});

// Update grid totals - improved version
function updateGridTotals() {
    const table = document.getElementById('transactionsTable');
    if (!table) return;
    
    const rows = Array.from(table.querySelectorAll('tbody tr'))
        .filter(row => row.style.display !== 'none');
    
    // Calculate total TKTs
    let totalTkts = 0;
    rows.forEach(row => {
        // Make sure we're accessing the correct column (index 3 is the 4th column)
        if (row.cells.length > 3) {
            const tktsValue = row.cells[3].textContent.trim();
            if (tktsValue && !isNaN(tktsValue)) {
                totalTkts += parseInt(tktsValue);
            }
        }
    });
    
    // Update grid total element
    const gridTotalTkts = document.getElementById('gridTotalTkts');
    if (gridTotalTkts) {
        gridTotalTkts.textContent = totalTkts;
    }
    
    console.log("Grid total updated: " + totalTkts); // Debug log
}

// Call updateGridTotals on page load and after any filtering
document.addEventListener('DOMContentLoaded', function() {
    // Initial update of grid totals
    setTimeout(updateGridTotals, 500);
    
    // Add event listeners for filter elements
    const filterElements = [
        document.getElementById('searchInput'),
        document.getElementById('statusFilter'),
        document.getElementById('filterDateBtn'),
        document.getElementById('resetFilterBtn')
    ];
    
    filterElements.forEach(element => {
        if (element) {
            if (element.tagName === 'BUTTON') {
                element.addEventListener('click', function() {
                    setTimeout(updateGridTotals, 100);
                });
            } else {
                element.addEventListener('change', function() {
                    setTimeout(updateGridTotals, 100);
                });
            }
        }
    });
});

