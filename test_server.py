#!/usr/bin/env python3
"""
Test script to verify server is responding
"""

import requests
import time

def test_server():
    print("Testing server connectivity...")
    
    try:
        # Test if server is responding
        print("Testing GET /login...")
        response = requests.get("http://localhost:8000/login", timeout=10)
        print(f"GET /login status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Server is responding to GET requests")
            
            # Now test login
            print("\nTesting POST /login...")
            login_data = {
                "email": "<EMAIL>",
                "password": "admin123"
            }
            
            response = requests.post("http://localhost:8000/login", data=login_data, allow_redirects=False, timeout=10)
            print(f"POST /login status: {response.status_code}")
            
            if response.status_code == 303:
                print("✅ Login successful - received redirect")
                if 'Set-Cookie' in response.headers:
                    print("✅ Token cookie set")
                    return True
                else:
                    print("❌ No token cookie set")
                    return False
            else:
                print(f"❌ Login failed with status: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                return False
        else:
            print(f"❌ Server not responding properly. Status: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError as e:
        print(f"❌ Connection error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    test_server()
