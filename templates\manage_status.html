{% extends "base.html" %}

{% block title %}Manage Statuses - Comsys Software PMO & CSO System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2>Manage Statuses</h2>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h4>Add New Status</h4>
                            </div>
                            <div class="card-body">
                                <form action="/status/create" method="post">
                                    <div class="mb-3">
                                        <label for="status_id" class="form-label">Status ID</label>
                                        <input type="number" class="form-control" id="status_id" name="status_id" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="status_name" class="form-label">Status Name</label>
                                        <input type="text" class="form-control" id="status_name" name="status_name" required>
                                    </div>
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-success">
                                            <i class="bi bi-plus-circle me-2"></i>Add Status
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Status ID</th>
                                <th>Status Name</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for status in statuses %}
                            <tr>
                                <td>{{ status.Status_ID }}</td>
                                <td>{{ status.Status_Name }}</td>
                                <td>
                                    <button class="btn btn-sm btn-warning edit-status-btn" 
                                            data-status-id="{{ status.Status_ID }}" 
                                            data-status-name="{{ status.Status_Name }}">
                                        <i class="bi bi-pencil"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-status-btn" 
                                            data-status-id="{{ status.Status_ID }}">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <a href="/status" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Status Transactions
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Status Modal -->
<div class="modal fade" id="editStatusModal" tabindex="-1" aria-labelledby="editStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editStatusModalLabel">Edit Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editStatusForm" action="/status/update-status" method="post">
                    <input type="hidden" id="edit_status_id" name="status_id">
                    <div class="mb-3">
                        <label for="edit_status_name" class="form-label">Status Name:</label>
                        <input type="text" class="form-control" id="edit_status_name" name="status_name" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveStatusBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Status Modal -->
<div class="modal fade" id="deleteStatusModal" tabindex="-1" aria-labelledby="deleteStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteStatusModalLabel">Delete Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this status? This action cannot be undone.</p>
                <p class="text-danger"><strong>Note:</strong> You cannot delete a status that has transactions. Delete all transactions for this status first.</p>
                <form id="deleteStatusForm" action="/status/delete-status" method="post">
                    <input type="hidden" id="delete
