import os
import re

def add_import_to_file(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
        
        # Check if UserLog is already imported
        if re.search(r'from\s+models\s+import\s+.*UserLog', content) or re.search(r'from\s+models\s+import\s+UserLog', content):
            print(f"✅ {file_path}: UserLog is already imported")
            return True
        
        # Find the models import line
        models_import = re.search(r'from\s+models\s+import\s+.*', content)
        if models_import:
            # Add UserLog to the existing import
            old_import = models_import.group(0)
            if old_import.endswith(','):
                new_import = old_import + ' UserLog'
            else:
                new_import = old_import + ', UserLog'
            
            content = content.replace(old_import, new_import)
            
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            print(f"✅ {file_path}: Added UserLog to existing models import")
            return True
        else:
            # Add a new import line
            import_line = 'from models import UserLog\n'
            
            # Find the first import line
            first_import = re.search(r'import\s+.*|from\s+.*\s+import\s+.*', content)
            if first_import:
                # Add the import after the first import line
                position = first_import.end()
                content = content[:position] + '\n' + import_line + content[position:]
            else:
                # Add the import at the beginning of the file
                content = import_line + content
            
            with open(file_path, 'w', encoding='utf-8') as file:
                file.write(content)
            
            print(f"✅ {file_path}: Added new UserLog import")
            return True
    except Exception as e:
        print(f"❌ Error fixing {file_path}: {str(e)}")
        return False

if __name__ == "__main__":
    # Fix the app.py file
    app_path = "app.py"
    if os.path.exists(app_path):
        print(f"\nFixing {app_path}...")
        add_import_to_file(app_path)
    else:
        print(f"❌ File not found: {app_path}")