
from sqlalchemy import Column, Integer, String, DateTime, Date, <PERSON><PERSON>an, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()


# Add these models to your existing models.py file

class Landline(Base):
    __tablename__ = "Landlines"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    Landline = Column(String(20), nullable=True)

class LandlineCall(Base):
    __tablename__ = "LandLinesCalls"
    
    # Add a primary key since the table doesn't have one
    ID = Column(Integer, primary_key=True, autoincrement=True)
    Date = Column(DateTime, nullable=True)
    LandlineNum = Column(Integer, ForeignKey("Landlines.id"), nullable=True)
    CallsCount = Column(Integer, nullable=True)
    
    # Add relationship to Landline
    landline = relationship("Landline", backref="calls")

class Product(Base):
    __tablename__ = "Product"
    
    ProductID = Column(Integer, primary_key=True)
    Product = Column(String(50), nullable=True)
    
    # Relationship to ProductTKTS
    tkts = relationship("ProductTKTS", back_populates="product")
    
    def __repr__(self):
        return f"<Product(ProductID={self.ProductID}, Product={self.Product})>"

class ProductTKTS(Base):
    __tablename__ = "Product_TKTS"
    
    ID = Column(Integer, primary_key=True, autoincrement=True)
    Date = Column(Date, nullable=True)
    Pro_ID = Column(Integer, ForeignKey("Product.ProductID"), nullable=True)
    Num_TKTS = Column(Integer, nullable=True)
    
    # Relationship to Product table
    product = relationship("Product", back_populates="tkts")
    
    def __repr__(self):
        return f"<ProductTKTS(ID={self.ID}, Date={self.Date}, Pro_ID={self.Pro_ID}, Num_TKTS={self.Num_TKTS})>"

class UserLog(Base):
    __tablename__ = "UserLogs"
    
    ID = Column(Integer, primary_key=True, autoincrement=True)
    Date = Column(DateTime, nullable=False, default=datetime.now)
    UserID = Column(Integer, ForeignKey("Users.ID"), nullable=False)
    UserName = Column(String(100), nullable=False)
    Type = Column(String(50), nullable=False)  # Add, Edit, Delete
    Name = Column(String(255), nullable=False)  # The name of the item being modified
    Num_TKTS = Column(Integer, nullable=False, default=0)
    
    # Relationship to User table
    user = relationship("User", back_populates="logs")
    
    def __repr__(self):
        return f"<UserLog(ID={self.ID}, Date={self.Date}, UserID={self.UserID}, Type={self.Type})>"

class User(Base):
    __tablename__ = "Users"
    
    ID = Column(Integer, primary_key=True, autoincrement=True)
    Username = Column(String(100), nullable=False, unique=True)
    Email = Column(String(255), nullable=False, unique=True)
    Password = Column(String(255), nullable=False)
    IsActive = Column(Boolean, nullable=False, default=True)
    IsAdmin = Column(Boolean, nullable=False, default=False)
    CanView = Column(Boolean, nullable=False, default=True)
    CanAdd = Column(Boolean, nullable=False, default=False)
    CanEdit = Column(Boolean, nullable=False, default=False)
    CanDelete = Column(Boolean, nullable=False, default=False)
    ResetToken = Column(String(255), nullable=True)
    ResetTokenExpiry = Column(DateTime, nullable=True)
    
    # Relationships
    logs = relationship("UserLog", back_populates="user")
    
    def __repr__(self):
        return f"<User(ID={self.ID}, Username={self.Username}, Email={self.Email})>"







