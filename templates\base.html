<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Comsys Software - PMO & CSO System{% endblock %}</title>
    
    <!-- Bootstrap & Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="/static/style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    {% block extra_css %}{% endblock %}

    <style>
        /* Sidebar Styling */
        #sidebar-menu {
            width: 220px;
            position: fixed;
            top: 130px;
            left: 0;
            height: 100%;
            overflow-y: auto;
            background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
            border-right: 1px solid #dee2e6;
            z-index: 1000;
        }

        #sidebar-menu .list-group-item.active {
            background-color: #0d1b2a;
            color: white;
            font-weight: bold;
        }

        /* Main Content Shift */
        #main-content {
            margin-left: 220px;
        }

        body {
            background-color: #bbd2f5;
        }
    </style>
</head>

<body class="d-flex flex-column min-vh-100 has-fixed-container">

<!-- Fixed Header -->
<header class="fixed-top text-white shadow d-flex align-items-center" style="height: 100px; width: 100vw; background: linear-gradient(to right, #1b263b, #0d1b2a); z-index: 1050;">
    <div class="w-100 px-4">
        <div class="d-flex justify-content-between align-items-center">
            <h3 class="mb-0 fs-2">
                <i class="bi bi-speedometer2 me-2"></i>
                Comsys Software PMO & CSO System
            </h3>
            {% if current_user %}
            <div>
                <a href="/logout" class="btn btn-sm btn-outline-light">
                    <i class="bi bi-box-arrow-right me-1"></i>Logout
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</header>


    <!-- Sidebar Menu -->
    {% if current_user %}
    <nav id="sidebar-menu">
        <div class="accordion" id="sidebarAccordion">

            <!-- Home Page -->
            <div class="list-group list-group-flush">
                <a href="/" class="list-group-item list-group-item-action {% if active_tab == 'home' %}active{% endif %}">
                    <i class="bi bi-house-door me-1"></i> Home Page
                </a>
            </div>

            <!-- Daily Reports -->
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button {% if active_tab not in ['channels', 'employees', 'products', 'sections', 'status', 'landlines'] %}collapsed{% endif %} bg-light"
                            type="button" data-bs-toggle="collapse" data-bs-target="#dailyReports">
                        <i class="bi bi-calendar3 me-2"></i> Daily Reports
                    </button>
                </h2>
                <div id="dailyReports" class="accordion-collapse collapse {% if active_tab in ['channels', 'employees', 'products', 'sections', 'status', 'landlines'] %}show{% endif %}"
                    data-bs-parent="#sidebarAccordion">
                    <div class="list-group list-group-flush">
                        <a href="/channels" class="list-group-item list-group-item-action {% if active_tab == 'channels' %}active{% endif %}">Channels</a>
                        <a href="/employees" class="list-group-item list-group-item-action {% if active_tab == 'employees' %}active{% endif %}">Employees</a>
                        <a href="/products" class="list-group-item list-group-item-action {% if active_tab == 'products' %}active{% endif %}">Products</a>
                        <a href="/sections" class="list-group-item list-group-item-action {% if active_tab == 'sections' %}active{% endif %}">Sections</a>
                        <a href="/status" class="list-group-item list-group-item-action {% if active_tab == 'status' %}active{% endif %}">Status</a>
                        <a href="/landlines" class="list-group-item list-group-item-action {% if active_tab == 'landlines' %}active{% endif %}">Landline Calls</a>
                    </div>
                </div>
            </div>

            <!-- CSO Reports -->
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed bg-light" type="button" data-bs-toggle="collapse" data-bs-target="#csoReports">
                        <i class="bi bi-briefcase-fill me-2"></i> CSO Reports
                    </button>
                </h2>
                <div id="csoReports" class="accordion-collapse collapse" data-bs-parent="#sidebarAccordion">
                    <div class="list-group list-group-flush">
                        <a href="/reports/cso-summary" class="list-group-item list-group-item-action">CSO Summary</a>
                        <a href="/reports/cso-details" class="list-group-item list-group-item-action">CSO Details</a>
                    </div>
                </div>
            </div>

            <!-- PMO Reports -->
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button collapsed bg-light" type="button" data-bs-toggle="collapse" data-bs-target="#pmoReportsSection">
                        <i class="bi bi-graph-up-arrow me-2"></i> PMO Reports
                    </button>
                </h2>
                <div id="pmoReportsSection" class="accordion-collapse collapse {% if active_tab in ['pmo-dashboard'] %}show{% endif %}" data-bs-parent="#sidebarAccordion">
                    <div class="list-group list-group-flush">
                        <a href="/reports/pmo-dashboard" class="list-group-item list-group-item-action">PMO Dashboard</a>
                    </div>
                </div>
            </div>

            <!-- User Log Report -->
            <div class="accordion-item">
                <h2 class="accordion-header">
                    <button class="accordion-button {% if active_tab == 'reports' %}{% else %}collapsed{% endif %} bg-light"
                            type="button" data-bs-toggle="collapse" data-bs-target="#userLogSection">
                        <i class="bi bi-clock-history me-1"></i> User Log
                    </button>
                </h2>
                <div id="userLogSection" class="accordion-collapse collapse {% if active_tab == 'reports' %}show{% endif %}" data-bs-parent="#sidebarAccordion">
                    <div class="list-group list-group-flush">
                        <a href="/reports/user-logs" class="list-group-item list-group-item-action {% if active_tab == 'reports' %}active{% endif %}">
                            User Logs
                        </a>
                    </div>
                </div>
            </div>


            <!-- Users (Admins Only) -->
            {% if current_user.IsAdmin %}
            <div class="list-group list-group-flush">
                <a href="/users" class="list-group-item list-group-item-action {% if active_tab == 'users' %}active{% endif %}">
                    <i class="bi bi-people-fill me-1"></i> Users
                </a>
            </div>
            {% endif %}
        </div>
    </nav>
    {% endif %}

    <!-- Main Content -->
    <main class="flex-grow-1 py-4" style="margin-top: 60px; margin-bottom: 60px;" id="main-content">
        <div class="container">
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Footer -->
    <footer class="fixed-bottom py-2 mt-auto border-top" style="background: linear-gradient(to right, #0d1b2a, #1b263b); color: #ffffff;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-4 text-center text-md-start">
                    {% if current_user %}
                    <p class="mb-0 small">
                        <i class="bi bi-person-circle me-1"></i>
                        Logged in as: <strong>{{ current_user.UserName }}</strong>
                    </p>
                    {% else %}
                    <p class="mb-0 small"><i class="bi bi-person-circle me-1"></i>Not logged in</p>
                    {% endif %}
                </div>
                <div class="col-md-4 text-center">
                    <p class="mb-0 small">
                        <i class="bi bi-code-slash me-1"></i>
                        Developed by <strong>Dr. Ahmed Rabea</strong>
                    </p>
                </div>
                <div class="col-md-4 text-center text-md-end">
                    <p class="mb-0 small">
                        <i class="bi bi-calendar-date me-1"></i>
                        <span id="current-date"></span>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
