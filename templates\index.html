{% extends "base.html" %}


{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<!-- Attractive Form Card -->
<div class="card mb-4 shadow-lg border-0" style="background: linear-gradient(to bottom right, #0d1b2a, #1b263b); color: #ffffff;">
    <div class="card-header text-white border-0" style="background: linear-gradient(to right, #1b263b, #0d1b2a);">
        <h2 class="mb-0"><i class="bi bi-plus-circle me-2"></i>Add New Channel TKTs</h2>
    </div>
    <div class="card-body">
        <form action="/submit" method="post">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date" class="form-label text-white">Date</label>
                    <input type="date" class="form-control bg-dark text-white border-secondary" id="date" name="date" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="channel_id" class="form-label text-white">Channel</label>
                    <select class="form-select bg-dark text-white border-secondary" id="channel_id" name="channel_id" required>
                        <option value="" selected disabled>Select Channel</option>
                        {% for channel in channels %}
                        <option value="{{ channel.ChannelID }}">{{ channel.Channel }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="num_tkts" class="form-label text-white">Number of TKTS</label>
                    <input type="number" class="form-control bg-dark text-white border-secondary" id="num_tkts" name="num_tkts" min="1" required>
                </div>
            </div>
            <div class="text-end mt-3">
                <button type="submit" class="btn btn-outline-light px-4 py-2">
                    <i class="bi bi-check-circle me-2"></i>Submit Entry
                </button>
            </div>
        </form>
    </div>
</div>


<!-- Filter Card -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h2>Filter & Export</h2>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Date Filter -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">Date Range Filter</div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-5">
                                <label for="startDate" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="startDate">
                            </div>
                            <div class="col-md-5">
                                <label for="endDate" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="endDate">
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button class="btn btn-primary w-100" id="filterDateBtn">
                                    <i class="bi bi-funnel me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Search & Export -->
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-header">Search & Export</div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-md-6">
                                <label for="searchInput" class="form-label">Search</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="searchInput" placeholder="Search...">
                                    <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <div class="btn-group w-100">
                                    <button class="btn btn-success" id="exportBtn">
                                        <i class="bi bi-file-excel me-1"></i>Export
                                    </button>
                                    <button class="btn btn-info" id="printBtn">
                                        <i class="bi bi-printer me-1"></i>Print
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Grid -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h2>Transactions History</h2>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="transactionsTable">
                <thead>
                    <tr>
                        <th data-sort="id">ID <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="date">Date <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="channel">Channel <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="tkts">TKTS <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.ID }}</td>
                        <td>{{ transaction.Date.strftime('%Y-%m-%d') if transaction.Date is not string else transaction.Date }}</td>
                        <td>{{ transaction.Channel }}</td>
                        <td>{{ transaction.Num_TKTS }}</td>
                        <td>
                            <button class="btn btn-sm btn-warning edit-btn" data-id="{{ transaction.ID }}" 
                                    data-date="{{ transaction.Date.strftime('%Y-%m-%d') }}" 
                                    data-channel="{{ transaction.Channel }}"
                                    data-channel-id="{{ transaction.Ch_ID }}"
                                    data-tkts="{{ transaction.Num_TKTS }}">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-btn" data-id="{{ transaction.ID }}">
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-secondary fw-bold">
                        <td colspan="3" class="text-end">Total:</td>
                        <td id="gridTotalTkts">0</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="card-footer bg-light">
        <strong>Total Records: <span id="recordCount">{{ transactions|length }}</span></strong>
    </div>
</div>

<!-- Dashboard -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h2>Dashboard</h2>
    </div>
    <div class="card-body">
        <div class="row">
            <!-- Summary Cards -->
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="card-title">Total TKTS</h3>
                        <h2 class="display-4" id="totalTkts">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="card-title">Channels</h3>
                        <h2 class="display-4" id="uniqueChannels">0</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body text-center">
                        <h3 class="card-title">Avg TKTS/Day</h3>
                        <h2 class="display-4" id="avgTkts">0</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts -->
        <div class="row mt-4">
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header">Channel Distribution</div>
                    <div class="card-body d-flex align-items-center justify-content-center">
                        <div style="height: 300px; width: 100%;">
                            <canvas id="channelChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-header">TKTS Trend</div>
                    <div class="card-body d-flex align-items-center justify-content-center">
                        <div style="height: 300px; width: 100%;">
                            <canvas id="trendChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editModalLabel">Edit Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" action="/update" method="post">
                    <input type="hidden" id="edit_id" name="id">
                    <div class="mb-3">
                        <label for="edit_date" class="form-label">Date:</label>
                        <input type="date" class="form-control" id="edit_date" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_channel_id" class="form-label">Channel:</label>
                        <select class="form-select" id="edit_channel_id" name="channel_id" required>
                            {% for channel in channels %}
                            <option value="{{ channel.ChannelID }}">{{ channel.Channel }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_num_tkts" class="form-label">Number of TKTS:</label>
                        <input type="number" class="form-control" id="edit_num_tkts" name="num_tkts" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEditBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this transaction? This action cannot be undone.</p>
                <input type="hidden" id="delete_id">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<script src="/static/script.js"></script>
<script>
    // Set today's date for all date inputs
    document.addEventListener('DOMContentLoaded', function() {
        const today = new Date();
        const formattedToday = today.toISOString().split('T')[0];
        
        // Find the latest transaction date
        let latestDate = formattedToday; // Default to today
        const table = document.getElementById('transactionsTable');
        if (table) {
            const dateColumn = 1; // Date is in the second column (index 1)
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 0) {
                // Get all dates from the table
                const dates = Array.from(rows).map(row => new Date(row.cells[dateColumn].textContent));
                // Find the latest date
                if (dates.length > 0) {
                    const maxDate = new Date(Math.max.apply(null, dates));
                    latestDate = maxDate.toISOString().split('T')[0];
                }
            }
        }
        
        // Set date inputs - both start and end date to latest transaction date
        const startDateInput = document.getElementById('startDate');
        if (startDateInput) {
            startDateInput.value = latestDate;
        }
        
        const endDateInput = document.getElementById('endDate');
        if (endDateInput) {
            endDateInput.value = latestDate;
        }
        
        // Set date input for new transaction to today
        const dateInput = document.getElementById('date');
        if (dateInput) {
            dateInput.value = formattedToday;
        }
        
        // Trigger filter to update dashboard
        const filterBtn = document.getElementById('filterDateBtn');
        if (filterBtn) {
            filterBtn.click();
        }
    });
</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Existing code...
        
        // Update grid totals specifically for this page
        function updateGridTotals() {
            const table = document.getElementById('transactionsTable');
            if (!table) return;
            
            const rows = Array.from(table.querySelectorAll('tbody tr'))
                .filter(row => row.style.display !== 'none');
            
            // Calculate total TKTs
            let totalTkts = 0;
            rows.forEach(row => {
                if (row.cells.length > 3) {
                    const tktsValue = row.cells[3].textContent.trim();
                    if (tktsValue && !isNaN(tktsValue)) {
                        totalTkts += parseInt(tktsValue);
                    }
                }
            });
            
            // Update grid total element
            const gridTotalTkts = document.getElementById('gridTotalTkts');
            if (gridTotalTkts) {
                gridTotalTkts.textContent = totalTkts;
            }
        }
        
        // Call updateGridTotals after filtering
        const originalFilterTable = filterTable;
        filterTable = function() {
            originalFilterTable();
            updateGridTotals();
        };
        
        // Initial update of grid totals
        setTimeout(updateGridTotals, 500);
    });
</script>
{% endblock %}





















