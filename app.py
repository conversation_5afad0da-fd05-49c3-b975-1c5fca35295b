# Clean up imports - JWT fix applied
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, date, timedelta
from typing import List, Optional, Union
from fastapi import FastAPI, Request, Form, HTTPException, Depends, Cookie, Response, status, Query
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy import create_engine, Column, Integer, String, Date, DateTime, ForeignKey, Boolean, func, inspect, literal
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session
from sqlalchemy.exc import SQLAlchemyError
from models import User, Landline, LandlineCall, UserLog, Product, ProductTKTS
from pydantic import BaseModel, EmailStr
import logging
from functools import lru_cache
import secrets
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import traceback
import jwt
import uvicorn
from sqlalchemy import text  # <-- Make sure to import text



# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Security settings
SECRET_KEY = "YOUR_SECRET_KEY_HERE"  # Change this in production!
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 8  # 8 hours

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Database setup with optimized connection pooling
try:
    logger.info("Initializing database connection...")
    SQLALCHEMY_DATABASE_URL = "mssql+pyodbc://Rabea:Comsys$<EMAIL>/CSOStat?driver=ODBC+Driver+17+for+SQL+Server&TrustServerCertificate=yes"
    
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_size=5,  # Maintain a pool of connections
        max_overflow=10,  # Allow up to 10 connections beyond pool_size
        pool_timeout=30,  # Wait up to 30 seconds for a connection
        pool_recycle=1800,  # Recycle connections after 30 minutes
        pool_pre_ping=True,  # Verify connections before using them
        fast_executemany=True  # Optimize batch operations
    )
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    Base = declarative_base()
    logger.info("Database connection initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize database: {str(e)}", exc_info=True)
    raise

# Models with optimized column types
class User(Base):
    __tablename__ = "Users"
    
    UserID = Column(Integer, primary_key=True, index=True)
    UserName = Column(String(100), unique=True, nullable=False)
    Email = Column(String(255), unique=True, nullable=False)
    Password = Column(String(255), nullable=True)
    IsActive = Column(Boolean, default=True, nullable=False)
    IsAdmin = Column(Boolean, default=False, nullable=False)
    CanView = Column(Boolean, default=True, nullable=False)
    CanAdd = Column(Boolean, default=False, nullable=False)
    CanEdit = Column(Boolean, default=False, nullable=False)
    CanDelete = Column(Boolean, default=False, nullable=False)
    ResetToken = Column(String(255), nullable=True)
    ResetTokenExpiry = Column(DateTime, nullable=True)

class Channel(Base):
    __tablename__ = "Channel"
    
    ChannelID = Column(Integer, primary_key=True)
    Channel = Column(String(100), index=True)
    
    # Add relationship for efficient joins
    transactions = relationship("ChannelTKTS", back_populates="channel")

class ChannelTKTS(Base):
    __tablename__ = "Channel_TKTS"
    
    ID = Column(Integer, primary_key=True, autoincrement=True)
    Date = Column(Date, index=True)
    Ch_ID = Column(Integer, ForeignKey("Channel.ChannelID"), index=True)
    Num_TKTS = Column(Integer)
    
    # Add relationship for efficient joins
    channel = relationship("Channel", back_populates="transactions")

# Agent models
class Agent(Base):
    __tablename__ = "Agents"
    
    Agent_Id = Column(Integer, primary_key=True)
    Agent_Name = Column(String(20), nullable=False)
    
    # Add relationship for efficient joins
    transactions = relationship("AgentTKTS", back_populates="agent")

class AgentTKTS(Base):
    __tablename__ = "Agent_TKTS"
    
    ID = Column(Integer, primary_key=True, autoincrement=True)
    Date = Column(Date, index=True)
    Agent_Id = Column(Integer, ForeignKey("Agents.Agent_Id"), index=True)
    Num_TKTS = Column(Integer)
    
    # Add relationship for efficient joins
    agent = relationship("Agent", back_populates="transactions")

# Product models
class Product(Base):
    __tablename__ = "Product"
    
    ProductID = Column(Integer, primary_key=True)
    # Don't specify any other columns

# Section models
class Section(Base):
    __tablename__ = "Section"
    
    SectionID = Column(Integer, primary_key=True)
    Section = Column(String(50))
    Description = Column(String(50))
    
    # Add relationship for efficient joins
    transactions = relationship("SectionTKTS", back_populates="section")

class SectionTKTS(Base):
    __tablename__ = "Section_TKTS"
    
    ID = Column(Integer, primary_key=True, autoincrement=True)
    Date = Column(Date, index=True)
    Sec_ID = Column(Integer, ForeignKey("Section.SectionID"), index=True)
    Num_TKTS = Column(Integer)
    
    # Add relationship for efficient joins
    section = relationship("Section", back_populates="transactions")

# Status models
class Status(Base):
    __tablename__ = "Status"
    
    Status_ID = Column(Integer, primary_key=True)
    Status_Name = Column(String(20), nullable=True)
    
    # Add relationship for efficient joins
    transactions = relationship("StatusTKTS", back_populates="status")


        
class StatusTKTS(Base):
    __tablename__ = "Status_TKTS"
    
    ID = Column(Integer, primary_key=True, autoincrement=True)
    Date = Column(Date, index=True)
    Status_ID = Column(Integer, ForeignKey("Status.Status_ID"), index=True)
    Num_TKTS = Column(Integer)
    
    # Add relationship for efficient joins
    status = relationship("Status", back_populates="transactions")

# Landline models
class Landline(Base):
    __tablename__ = "Landlines"
    
    id = Column(Integer, primary_key=True)
    Landline = Column(String(50))
    
    # Add relationship
    calls = relationship("LandlineCall", back_populates="landline")


# Update the LandlineCall model to ensure it has an ID field
class LandlineCall(Base):
    __tablename__ = "LandLinesCalls"
    
    # Change this to match the actual column name in your database
    # It might be "ID", "Id", or "id" depending on your database schema
    id = Column(Integer, primary_key=True, autoincrement=True)
    Date = Column(DateTime)
    LandlineNum = Column(Integer, ForeignKey("Landlines.id"))
    CallsCount = Column(Integer)
    
    # Add relationship if needed
    landline = relationship("Landline", back_populates="calls")


# Pydantic models for API
class TokenData(BaseModel):
    username: Optional[str] = None

class Token(BaseModel):
    access_token: str
    token_type: str

class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str
    is_admin: bool = False
    can_view: bool = True
    can_add: bool = False
    can_edit: bool = False
    can_delete: bool = False

class UserUpdate(BaseModel):
    email: EmailStr
    is_active: bool = True
    is_admin: bool = False
    can_view: bool = True
    can_add: bool = False
    can_edit: bool = False
    can_delete: bool = False

# Database dependency with context manager
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Authentication functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password):
    return pwd_context.hash(password)

def get_user(db: Session, username: str = None, email: str = None):
    if username:
        return db.query(User).filter(User.UserName == username).first()
    elif email:
        return db.query(User).filter(User.Email == email).first()
    return None

def authenticate_user(db: Session, email: str, password: str):
    user = get_user(db, email=email)
    if not user or not verify_password(password, user.Password):
        return False
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

# Email functions
def send_reset_email(email: str, reset_token: str, request: Request):
    try:
        # Generate reset URL
        reset_url = f"{request.url.scheme}://{request.url.netloc}/reset-password/{reset_token}"
        
        # Log the reset link for development/testing
        logger.info(f"Password reset link for {email}: {reset_url}")
        
        # In a production environment, you would configure these with real values
        sender_email = "<EMAIL>"  # Replace with real email
        sender_password = "your-email-password"  # Replace with real password
        
        # Create message
        message = MIMEMultipart()
        message["From"] = sender_email
        message["To"] = email
        message["Subject"] = "Password Reset - Channel TKTS System"
        
        # Email body
        body = f'''
        <html>
        <body>
            <h2>Password Reset Request</h2>
            <p>You requested a password reset for your Channel TKTS System account.</p>
            <p>Click the link below to reset your password:</p>
            <p><a href="{reset_url}">{reset_url}</a></p>
            <p>This link will expire in 1 hour.</p>
            <p>If you didn't request this reset, please ignore this email.</p>
        </body>
        </html>
        '''
        
        message.attach(MIMEText(body, "html"))
        
        # For development/testing, just log the reset link instead of sending email
        # In production, uncomment the below code and configure with real SMTP settings
        
        # Uncomment for production use with proper email credentials
        """
        # Connect to server and send email
        with smtplib.SMTP_SSL("smtp.example.com", 465) as server:
            server.login(sender_email, sender_password)
            server.send_message(message)
        """
        
        # For now, just return success and use the logged link for testing
        logger.info("Email would be sent in production. Check logs for reset link.")
        return True
            
    except Exception as e:
        logger.error(f"Failed to send reset email: {str(e)}", exc_info=True)
        return False

# Cache for frequently accessed data
@lru_cache(maxsize=1, typed=False)
def get_channels_cached(db_func):
    db = SessionLocal()
    try:
        return db.query(Channel).all()
    finally:
        db.close()

@lru_cache(maxsize=1, typed=False)
def get_agents_cached(db_func):
    db = SessionLocal()
    try:
        return db.query(Agent).all()
    finally:
        db.close()

@lru_cache(maxsize=1)
def get_sections_cached(db_func):
    db = next(db_func())
    try:
        return db.query(Section).all()
    finally:
        db.close()

@lru_cache(maxsize=1)
def get_statuses_cached(db_func):
    db = SessionLocal()
    try:
        return db.query(Status).all()
    finally:
        db.close()

# Initialize admin user
def init_admin():
    db = SessionLocal()
    try:
        admin = db.query(User).filter(User.UserName == "admin").first()
        if not admin:
            logger.info("Creating admin user...")
            admin_user = User(
                UserName="admin",
                Email="<EMAIL>",
                Password=get_password_hash("admin"),  # Change this in production
                IsActive=True,
                IsAdmin=True,
                CanView=True,
                CanAdd=True,
                CanEdit=True,
                CanDelete=True
            )
            db.add(admin_user)
            db.commit()
            logger.info("Admin user created successfully")
    except Exception as e:
        logger.error(f"Failed to initialize admin user: {str(e)}", exc_info=True)
    finally:
        db.close()

# FastAPI app
app = FastAPI(title="Channel TKTS System", version="2.0")
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Add global exception handler after app is defined
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    error_msg = f"Unhandled exception: {str(exc)}"
    stack_trace = traceback.format_exc()
    logger.error(f"{error_msg}\n{stack_trace}")
    return templates.TemplateResponse(
        "error.html",
        {"request": request, "error": "An internal server error occurred. Please check the server logs for details."}
    )

# Authentication middleware
async def get_current_user(token: str = Cookie(None), db: Session = Depends(get_db)):
    if not token:
        return None
    
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        token_data = TokenData(username=username)
    except:
        return None
    
    user = get_user(db, username=token_data.username)
    if user is None or not user.IsActive:
        return None
    return user

# Middleware to add cache control headers to all responses
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    
    # Add security headers to all responses
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    
    # For protected routes, add cache control headers
    if request.url.path not in ["/login", "/register", "/forgot-password", "/static"] and not request.url.path.startswith("/reset-password"):
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
    
    return response

# Routes
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request, current_user: User = Depends(get_current_user)):
    if current_user:
        return RedirectResponse(url="/", status_code=303)
    return templates.TemplateResponse("login.html", {"request": request})

@app.post("/login")
async def login(
    request: Request,
    response: Response,
    email: str = Form(...),
    password: str = Form(...),
    remember: bool = Form(default=False),  # Fixed: only use default=False, not both positional and keyword
    db: Session = Depends(get_db)
):
    # Log the login attempt for debugging
    logger.info(f"Login attempt for email: {email}")
    
    user = authenticate_user(db, email, password)
    if not user:
        logger.warning(f"Failed login attempt for email: {email}")
        return templates.TemplateResponse(
            "login.html", 
            {"request": request, "error": "Invalid email or password"}
        )
    
    if not user.IsActive:
        logger.warning(f"Inactive account login attempt: {email}")
        return templates.TemplateResponse(
            "login.html", 
            {"request": request, "error": "Account is inactive. Please contact an administrator."}
        )
    
    # Login successful
    logger.info(f"Successful login for user: {user.UserName}")
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES if remember else 60)
    access_token = create_access_token(
        data={"sub": user.UserName}, expires_delta=access_token_expires
    )
    
    # Set cookie for browser-based auth
    response = RedirectResponse(url="/", status_code=303)
    response.set_cookie(
        key="token",
        value=access_token,
        httponly=True,
        max_age=access_token_expires.total_seconds() if remember else None,
        samesite="lax"
    )
    
    return response

@app.get("/register", response_class=HTMLResponse)
async def register_page(request: Request, current_user: User = Depends(get_current_user)):
    if current_user:
        return RedirectResponse(url="/", status_code=303)
    return templates.TemplateResponse("register.html", {"request": request})

@app.post("/register")
async def register(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    password: str = Form(...),
    confirm_password: str = Form(...),
    db: Session = Depends(get_db)
):
    # Validate passwords match
    if password != confirm_password:
        return templates.TemplateResponse(
            "register.html", 
            {"request": request, "error": "Passwords do not match"}
        )
    
    # Check if username or email already exists
    existing_user = get_user(db, username=username)
    if existing_user:
        return templates.TemplateResponse(
            "register.html", 
            {"request": request, "error": "Username already exists"}
        )
    
    existing_email = get_user(db, email=email)
    if existing_email:
        return templates.TemplateResponse(
            "register.html", 
            {"request": request, "error": "Email already exists"}
        )
    
    # Create new user
    new_user = User(
        UserName=username,
        Email=email,
        Password=get_password_hash(password),
        IsActive=True,
        IsAdmin=False,
        CanView=True,
        CanAdd=False,
        CanEdit=False,
        CanDelete=False
    )
    
    db.add(new_user)
    db.commit()
    
    return RedirectResponse(url="/login?registered=true", status_code=303)

@app.get("/forgot-password", response_class=HTMLResponse)
async def forgot_password_page(request: Request):
    return templates.TemplateResponse("forgot_password.html", {"request": request})

@app.post("/forgot-password")
async def forgot_password(
    request: Request,
    email: str = Form(...),
    db: Session = Depends(get_db)
):
    user = get_user(db, email=email)
    if not user:
        return templates.TemplateResponse(
            "forgot_password.html", 
            {"request": request, "error": "Email not found"}
        )
    
    # Generate reset token
    reset_token = secrets.token_urlsafe(32)
    reset_expiry = datetime.utcnow() + timedelta(hours=1)
    
    # Update user with reset token
    user.ResetToken = reset_token
    user.ResetTokenExpiry = reset_expiry
    db.commit()
    
    # Send reset email
    email_sent = send_reset_email(email, reset_token, request)
    
    if email_sent:
        return templates.TemplateResponse(
            "forgot_password.html", 
            {"request": request, "success": "Password reset link has been sent to your email"}
        )
    else:
        return templates.TemplateResponse(
            "forgot_password.html", 
            {"request": request, "error": "Failed to send reset email. Please try again later."}
        )

@app.get("/reset-password/{token}", response_class=HTMLResponse)
async def reset_password_page(request: Request, token: str, db: Session = Depends(get_db)):
    # Verify token
    user = db.query(User).filter(User.ResetToken == token).first()
    if not user or user.ResetTokenExpiry < datetime.utcnow():
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "Invalid or expired reset token"}
        )
    
    return templates.TemplateResponse("reset_password.html", {"request": request, "token": token})

@app.post("/reset-password/{token}")
async def reset_password(
    request: Request,
    token: str,
    password: str = Form(...),
    confirm_password: str = Form(...),
    db: Session = Depends(get_db)
):
    # Validate passwords match
    if password != confirm_password:
        return templates.TemplateResponse(
            "reset_password.html", 
            {"request": request, "token": token, "error": "Passwords do not match"}
        )
    
    # Verify token
    user = db.query(User).filter(User.ResetToken == token).first()
    if not user or user.ResetTokenExpiry < datetime.utcnow():
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "Invalid or expired reset token"}
        )
    
    # Update password and clear token
    user.Password = get_password_hash(password)
    user.ResetToken = None
    user.ResetTokenExpiry = None
    db.commit()
    
    return templates.TemplateResponse(
        "reset_password.html", 
        {"request": request, "success": "Password has been reset successfully"}
    )

@app.get("/logout")
async def logout(response: Response):
    # Create redirect response
    response = RedirectResponse(url="/login", status_code=303)
    
    # Clear the token cookie
    response.delete_cookie(key="token")
    
    # Add cache control headers to prevent back button from showing protected pages
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    
    return response

# for Home Page

@app.get("/", response_class=HTMLResponse)
async def homepage(
    request: Request,
    response: Response,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db),
    from_date: str = Query(None),
    to_date: str = Query(None)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)

    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"

    # تاريخ أول يوم في الشهر الحالي
    default_from_date = date.today().replace(day=1)

    # تاريخ آخر سجل في قاعدة البيانات
    last_date_record = db.query(func.max(ChannelTKTS.Date)).scalar()
    default_to_date = last_date_record or date.today()

    # تحويل قيم query إذا كانت موجودة
    try:
        from_dt = datetime.strptime(from_date, "%Y-%m-%d").date() if from_date else default_from_date
        to_dt = datetime.strptime(to_date, "%Y-%m-%d").date() if to_date else default_to_date
    except ValueError:
        from_dt, to_dt = default_from_date, default_to_date

    # تطبيق الفلترة
    query = db.query(ChannelTKTS).filter(ChannelTKTS.Date >= from_dt, ChannelTKTS.Date <= to_dt)

    # إجماليات
    total_tkts = query.with_entities(func.sum(ChannelTKTS.Num_TKTS)).scalar() or 0
    total_channels = query.with_entities(ChannelTKTS.Ch_ID).distinct().count()
    days_count = query.with_entities(ChannelTKTS.Date).distinct().count() or 1
    avg_tkts_per_day = round(total_tkts / days_count, 2)

    # التوزيع حسب القنوات
    channel_distribution = (
        query.join(Channel, Channel.ChannelID == ChannelTKTS.Ch_ID)
        .with_entities(Channel.Channel, func.sum(ChannelTKTS.Num_TKTS))
        .group_by(Channel.Channel)
        .all()
    )

    # ترند الأيام
    trend = (
        query.with_entities(ChannelTKTS.Date, func.sum(ChannelTKTS.Num_TKTS))
        .group_by(ChannelTKTS.Date)
        .order_by(ChannelTKTS.Date)
        .all()
    )

    # تجهيز بيانات الرسم البياني
    channel_labels = [row[0] for row in channel_distribution]
    channel_values = [row[1] for row in channel_distribution]
    trend_labels = [row[0].strftime('%Y-%m-%d') for row in trend]
    trend_values = [row[1] for row in trend]

    return templates.TemplateResponse(
        "home.html",
        {
            "request": request,
            "current_user": current_user,
            "active_tab": "home",
            "image_path": "/static/images/logo.jpg",
            "total_tkts": total_tkts,
            "total_channels": total_channels,
            "avg_tkts_per_day": avg_tkts_per_day,
            "channel_labels": channel_labels,
            "channel_values": channel_values,
            "trend_labels": trend_labels,
            "trend_values": trend_values,
            "from_date": from_dt.strftime('%Y-%m-%d'),
            "to_date": to_dt.strftime('%Y-%m-%d'),
        }
    )



#Channel Page
@app.get("/channels", response_class=HTMLResponse)
async def index(
    request: Request, 
    response: Response,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanView:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to view this page", "current_user": current_user}
        )
    
    
    # Add cache control headers to prevent back button access after logout
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    
    # Get channels (cached)
    channels = get_channels_cached(get_db)
    
    # Optimized query with joins and specific column selection
    transactions_query = (
        db.query(
            ChannelTKTS.ID,
            ChannelTKTS.Date,
            Channel.Channel,
            ChannelTKTS.Num_TKTS,
            ChannelTKTS.Ch_ID
        )
        .join(Channel, ChannelTKTS.Ch_ID == Channel.ChannelID)
        .order_by(ChannelTKTS.Date.desc())
    )
    
    transactions = transactions_query.all()
    
    return templates.TemplateResponse(
        "channels.html", 
        {
            "request": request, 
            "transactions": transactions, 
            "channels": channels,
            "current_user": current_user,
            "active_tab": "channels"
        }
    )


@app.post("/channels/submit")
async def submit_form(
    request: Request, 
    date: date = Form(...), 
    channel_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanAdd:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to add records"}
        )
    
    try:
        new_record = ChannelTKTS(Date=date, Ch_ID=channel_id, Num_TKTS=num_tkts)
        db.add(new_record)
        db.commit()
        
        # Clear cache after data change
        get_channels_cached.cache_clear()
        
        return RedirectResponse(url="/channels", status_code=303)
    except Exception as e:
        db.rollback()   
        logger.error(f"Error submitting form: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/channels/update")
async def update_transaction(
    request: Request, 
    id: int = Form(...), 
    date: date = Form(...), 
    channel_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanEdit:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to edit records"}
        )
    
    try:
        transaction = db.query(ChannelTKTS).filter(ChannelTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        transaction.Date = date
        transaction.Ch_ID = channel_id
        transaction.Num_TKTS = num_tkts
        
        db.commit()
        return RedirectResponse(url="/channels", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating transaction: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.delete("/delete/{id}")
async def delete_transaction(
    id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    if not current_user.CanDelete:
        raise HTTPException(status_code=403, detail="Not authorized to delete records")
    
    try:
        transaction = db.query(ChannelTKTS).filter(ChannelTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        db.delete(transaction)
        db.commit()
        return {"success": True}
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting transaction: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


# User management routes
@app.get("/users", response_class=HTMLResponse)
async def users_page(
    request: Request, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user or not current_user.IsAdmin:
        return RedirectResponse(url="/login", status_code=303)
    
    users = db.query(User).all()
    return templates.TemplateResponse(
        "users.html", 
        {"request": request, "users": users, "current_user": current_user, "active_tab": "users"}
    )

@app.post("/users/create")
async def create_user(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    password: str = Form(...),
    is_admin: bool = Form(False),
    can_view: bool = Form(True),
    can_add: bool = Form(False),
    can_edit: bool = Form(False),
    can_delete: bool = Form(False),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user or not current_user.IsAdmin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # Check if username already exists
    existing_user = db.query(User).filter(User.UserName == username).first()
    if existing_user:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "Username already exists"}
        )
    
    # Create new user
    new_user = User(
        UserName=username,
        Email=email,
        Password=get_password_hash(password),
        IsActive=True,
        IsAdmin=is_admin,
        CanView=can_view,
        CanAdd=can_add,
        CanEdit=can_edit,
        CanDelete=can_delete
    )
    
    db.add(new_user)
    db.commit()
    
    return RedirectResponse(url="/users", status_code=303)

@app.post("/users/update/{user_id}")
async def update_user(
    user_id: int,
    request: Request,
    email: str = Form(...),
    is_active: bool = Form(False),
    is_admin: bool = Form(False),
    can_view: bool = Form(False),
    can_add: bool = Form(False),
    can_edit: bool = Form(False),
    can_delete: bool = Form(False),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user or not current_user.IsAdmin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    user = db.query(User).filter(User.UserID == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.Email = email
    user.IsActive = is_active
    user.IsAdmin = is_admin
    user.CanView = can_view
    user.CanAdd = can_add
    user.CanEdit = can_edit
    user.CanDelete = can_delete
    
    db.commit()
    
    return RedirectResponse(url="/users", status_code=303)

@app.post("/users/reset-password/{user_id}")
async def reset_password(
    user_id: int,
    request: Request,
    password: str = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user or not current_user.IsAdmin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    user = db.query(User).filter(User.UserID == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    user.Password = get_password_hash(password)
    db.commit()
    
    return RedirectResponse(url="/users", status_code=303)

@app.delete("/users/delete/{user_id}")
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user or not current_user.IsAdmin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # Prevent deleting your own account
    if current_user.UserID == user_id:
        raise HTTPException(status_code=400, detail="You cannot delete your own account")
    
    # Prevent deleting the main admin account
    user = db.query(User).filter(User.UserID == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    if user.UserName == "admin":
        raise HTTPException(status_code=400, detail="Cannot delete the main admin account")
    
    try:
        db.delete(user)
        db.commit()
        return {"success": True}
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting user: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

# Employee routes
@app.get("/employees", response_class=HTMLResponse)
async def employees_page(
    request: Request, 
    response: Response,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanView:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to view this page", "current_user": current_user}
        )
    
    # Add cache control headers to prevent back button access after logout
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    
    # Get agents (cached)
    agents = get_agents_cached(get_db)
    
    # Optimized query with joins and specific column selection
    transactions_query = (
        db.query(
            AgentTKTS.ID,
            AgentTKTS.Date,
            Agent.Agent_Name,
            AgentTKTS.Num_TKTS,
            AgentTKTS.Agent_Id
        )
        .join(Agent, AgentTKTS.Agent_Id == Agent.Agent_Id)
        .order_by(AgentTKTS.Date.desc())
    )
    
    transactions = transactions_query.all()
    
    return templates.TemplateResponse(
        "employees.html", 
        {
            "request": request, 
            "transactions": transactions, 
            "agents": agents,
            "current_user": current_user,
            "active_tab": "employees"
        }
    )

@app.post("/employees/submit")
async def submit_employee_form(
    request: Request, 
    date: date = Form(...), 
    agent_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanAdd:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to add records"}
        )
    
    try:
        new_record = AgentTKTS(Date=date, Agent_Id=agent_id, Num_TKTS=num_tkts)
        db.add(new_record)
        db.commit()
        
        # Clear cache after data change
        get_agents_cached.cache_clear()
        
        return RedirectResponse(url="/employees", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error submitting form: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/employees/update")
async def update_employee_transaction(
    request: Request, 
    id: int = Form(...), 
    date: date = Form(...), 
    agent_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanEdit:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to edit records"}
        )
    
    try:
        transaction = db.query(AgentTKTS).filter(AgentTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        transaction.Date = date
        transaction.Agent_Id = agent_id
        transaction.Num_TKTS = num_tkts
        
        db.commit()
        return RedirectResponse(url="/employees", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating transaction: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.delete("/employees/delete/{id}")
async def delete_employee_transaction(
    id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    if not current_user.CanDelete:
        raise HTTPException(status_code=403, detail="Not authorized to delete records")
    
    try:
        transaction = db.query(AgentTKTS).filter(AgentTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        db.delete(transaction)
        db.commit()
        return {"success": True}
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting transaction: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/employees/transactions")
async def get_employee_transactions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    if not current_user.CanView:
        raise HTTPException(status_code=403, detail="Not authorized to view records")
    
    # Optimized query with joins and specific column selection
    transactions_query = (
        db.query(
            AgentTKTS.ID,
            AgentTKTS.Date,
            Agent.Agent_Name,
            AgentTKTS.Num_TKTS,
            AgentTKTS.Agent_Id
        )
        .join(Agent, AgentTKTS.Agent_Id == Agent.Agent_Id)
        .order_by(AgentTKTS.Date.desc())
    )
    
    transactions = transactions_query.all()
    
    # Convert to serializable format
    result = []
    for t in transactions:
        result.append({
            "ID": t.ID,
            "Date": t.Date.strftime('%Y-%m-%d'),
            "Agent_Name": t.Agent_Name,
            "Num_TKTS": t.Num_TKTS,
            "Agent_Id": t.Agent_Id
        })
    
    return {"transactions": result}

# User log transaction report
@app.get("/reports", response_class=HTMLResponse)
async def reports_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanView:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to view this page", "current_user": current_user}
        )
    
    try:
        return templates.TemplateResponse(
            "reports.html", 
            {
                "request": request,
                "current_user": current_user,
                "active_tab": "reports"
            }
        )
    except Exception as e:
        logger.error(f"Error in reports page: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": f"An error occurred: {str(e)}", "current_user": current_user}
        )

# Make sure the user_logs route is correctly defined
@app.get("/reports/user-logs", response_class=HTMLResponse)
async def user_logs_report_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanView:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to view this page", "current_user": current_user}
        )
    
    try:
        # Get user activity logs
        logs = []
        
        # Channel transactions
        channel_logs = (
            db.query(
                ChannelTKTS.ID,
                ChannelTKTS.Date,
                Channel.Channel.label("Name"),
                ChannelTKTS.Num_TKTS,
                literal("Channel").label("Type"),
                literal(current_user.UserName).label("UserName")
            )
            .join(Channel, ChannelTKTS.Ch_ID == Channel.ChannelID)
            .order_by(ChannelTKTS.Date.desc())
            .all()
        )
        
        # Agent transactions
        agent_logs = (
            db.query(
                AgentTKTS.ID,
                AgentTKTS.Date,
                Agent.Agent_Name.label("Name"),
                AgentTKTS.Num_TKTS,
                literal("Employee").label("Type"),
                literal(current_user.UserName).label("UserName")
            )
            .join(Agent, AgentTKTS.Agent_Id == Agent.Agent_Id)
            .order_by(AgentTKTS.Date.desc())
            .all()
        )
        
        # Product transactions - using raw SQL to avoid attribute issues
        product_logs = []
        try:
            # Get product name column from information schema
            product_columns = db.execute(text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Product'")).fetchall()
            product_column_names = [row[0] for row in product_columns]
            
            # Find the name column (assuming it's the second column after ProductID)
            name_column = product_column_names[1] if len(product_column_names) > 1 else "ProductID"
            
            # Get product transactions with product names
            product_rows = db.execute(f"""
                SELECT pt.ID, pt.Date, p.{name_column} as Name, pt.Num_TKTS, 'Product' as Type, '{current_user.UserName}' as UserName
                FROM Product_TKTS pt
                JOIN Product p ON pt.Pro_ID = p.ProductID
                ORDER BY pt.Date DESC
            """).fetchall()
            
            for row in product_rows:
                product_logs.append({
                    "ID": row[0],
                    "Date": row[1],
                    "Name": row[2],
                    "Num_TKTS": row[3],
                    "Type": row[4],
                    "UserName": row[5]
                })
        except Exception as e:
            logger.error(f"Error getting product logs: {str(e)}", exc_info=True)
        
        # Section transactions
        section_logs = (
            db.query(
                SectionTKTS.ID,
                SectionTKTS.Date,
                Section.Section.label("Name"),
                SectionTKTS.Num_TKTS,
                literal("Section").label("Type"),
                literal(current_user.UserName).label("UserName")
            )
            .join(Section, SectionTKTS.Sec_ID == Section.SectionID)
            .order_by(SectionTKTS.Date.desc())
            .all()
        )
        
        # Status transactions
        status_logs = (
            db.query(
                StatusTKTS.ID,
                StatusTKTS.Date,
                Status.Status_Name.label("Name"),
                StatusTKTS.Num_TKTS,
                literal("Status").label("Type"),
                literal(current_user.UserName).label("UserName")
            )
            .join(Status, StatusTKTS.Status_ID == Status.Status_ID)
            .order_by(StatusTKTS.Date.desc())
            .all()
        )
        
        # Landline calls
        landline_logs = []
        try:
            landline_logs = (
                db.query(
                    LandlineCall.ID,
                    LandlineCall.Date,
                    Landline.Landline.label("Name"),
                    LandlineCall.CallsCount.label("Num_TKTS"),
                    literal("Landline").label("Type"),
                    literal(current_user.UserName).label("UserName")
                )
                .join(Landline, LandlineCall.LandlineNum == Landline.id)
                .order_by(LandlineCall.Date.desc())
                .all()
            )
        except Exception as e:
            logger.error(f"Error getting landline logs: {str(e)}", exc_info=True)
        
        # Combine all logs
        logs = channel_logs + agent_logs + product_logs + section_logs + status_logs + landline_logs
        
        # Sort combined results by date
        logs.sort(key=lambda x: x.Date if hasattr(x, 'Date') else x['Date'], reverse=True)
        
        return templates.TemplateResponse(
            "user_logs.html", 
            {
                "request": request, 
                "logs": logs,
                "current_user": current_user,
                "active_tab": "reports"
            }
        )
    except Exception as e:
        logger.error(f"Error in user logs report: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": f"An error occurred: {str(e)}", "current_user": current_user}
        )

# Products page
@app.get("/products", response_class=HTMLResponse)
async def products_page(
    request: Request, 
    response: Response,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        if not current_user:
            return RedirectResponse(url="/login", status_code=303)
        
        if not current_user.CanView:
            return templates.TemplateResponse(
                "error.html", 
                {"request": request, "error": "You don't have permission to view this page", "current_user": current_user}
            )
        
        # Add cache control headers
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"
        
        # Get column names for Product table
        product_columns = db.execute(text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Product'")).fetchall()
        product_column_names = [row[0] for row in product_columns]
        
        # Find the name column (assuming it's the second column after ProductID)
        name_column = product_column_names[1] if len(product_column_names) > 1 else "ProductID"
        
        # Get products using raw SQL
        products = []
        product_rows = db.execute(text(f"SELECT ProductID, {name_column} FROM Product")).fetchall()
        for row in product_rows:
            products.append({"ProductID": row[0], "Name": row[1]})
        
        # Get transactions using raw SQL with explicit column names
        transactions = []
        transaction_rows = db.execute(text(f"""
            SELECT pt.ID, pt.Date, p.{name_column}, pt.Num_TKTS, pt.Pro_ID 
            FROM Product_TKTS pt
            JOIN Product p ON pt.Pro_ID = p.ProductID
            ORDER BY pt.Date DESC
        """)).fetchall()
        
        for row in transaction_rows:
            transactions.append({
                "ID": row[0],
                "Date": row[1],
                "ProductName": row[2],
                "Num_TKTS": row[3],
                "Pro_ID": row[4]
            })
        
        return templates.TemplateResponse(
            "products.html", 
            {
                "request": request, 
                "transactions": transactions, 
                "products": products,
                "current_user": current_user,
                "active_tab": "products"
            }
        )
    except Exception as e:
        logger.error(f"Error in products_page: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": f"An error occurred: {str(e)}", "current_user": current_user}
        )

@app.post("/products/submit")
async def submit_product_form(
    request: Request, 
    date: date = Form(...), 
    product_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanAdd:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to add records"}
        )
    
    try:
        new_record = ProductTKTS(Date=date, Pro_ID=product_id, Num_TKTS=num_tkts)
        db.add(new_record)
        db.commit()
        
        # Clear cache after data change
        get_products_cached.cache_clear()
        
        return RedirectResponse(url="/products", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error submitting form: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/products/update")
async def update_product_transaction(
    request: Request, 
    id: int = Form(...), 
    date: date = Form(...), 
    product_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanEdit:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to edit records"}
        )
    
    try:
        transaction = db.query(ProductTKTS).filter(ProductTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        transaction.Date = date
        transaction.Pro_ID = product_id
        transaction.Num_TKTS = num_tkts
        
        db.commit()
        return RedirectResponse(url="/products", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating transaction: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.delete("/products/delete/{id}")
async def delete_product_transaction(
    id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        raise HTTPException(status_code=401, detail="Not authenticated")
    
    if not current_user.CanDelete:
        raise HTTPException(status_code=403, detail="Not authorized to delete records")
    
    try:
        transaction = db.query(ProductTKTS).filter(ProductTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        db.delete(transaction)
        db.commit()
        return {"success": True}
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting transaction: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))

# Add cached function for products
@lru_cache(maxsize=1)
def get_products_cached(get_db_func):
    db = next(get_db_func())
    try:
        products = db.execute("SELECT * FROM Product").fetchall()
        result = []
        for row in products:
            result.append({"ProductID": row.ProductID, "Name": row[1]})  # Adjust index based on actual column position
        return result
    except Exception as e:
        logger.error(f"Error in get_products_cached: {str(e)}", exc_info=True)
        return []
    finally:
        db.close()

# Create tables and initialize admin
@app.on_event("startup")
async def startup_event():
    try:
        # Create tables if they don't exist
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
        # Initialize admin user
        init_admin()
    except Exception as e:
        logger.error(f"Error during startup: {str(e)}", exc_info=True)
        raise

@app.get("/check-tables")
async def check_tables(request: Request):
    try:
        # Check if tables exist in the database
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        # Get column information for the Product table if it exists
        product_columns = []
        if "Product" in tables:
            product_columns = [column['name'] for column in inspector.get_columns('Product')]
        
        return {
            "tables": tables,
            "product_columns": product_columns
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/check-product-table")
async def check_product_table(request: Request):
    try:
        # Check if Product table exists
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if "Product" in tables:
            # Get column information for the Product table
            columns = inspector.get_columns('Product')
            column_names = [column['name'] for column in columns]
            
            # Get primary key information
            pk = inspector.get_pk_constraint('Product')
            
            return {
                "table_exists": True,
                "columns": column_names,
                "primary_key": pk['constrained_columns'] if pk else None
            }
        else:
            return {"table_exists": False}
    except Exception as e:
        return {"error": str(e)}

@app.get("/debug-product")
async def debug_product(request: Request, db: Session = Depends(get_db)):
    try:
        # Get raw SQL column names
        result = db.execute("SELECT TOP 1 * FROM Product").first()
        if result:
            column_names = result._mapping.keys()
            
            # Get all products
            products = db.query(Product).all()
            product_data = []
            
            for product in products:
                product_dict = {}
                for column in dir(product):
                    if not column.startswith('_') and column != 'metadata' and column != 'transactions':
                        product_dict[column] = getattr(product, column)
                product_data.append(product_dict)
            
            return {
                "column_names": list(column_names),
                "products": product_data
            }
        else:
            return {"error": "No products found"}
    except Exception as e:
        return {"error": str(e)}

@app.get("/debug/landlines")
async def debug_landlines(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user or not current_user.IsAdmin:
        return JSONResponse(status_code=403, content={"status": "error", "message": "Not authorized"})
    
    try:
        # Get all landline calls with their IDs
        landline_calls = db.query(LandlineCall).all()
        
        # Convert to list of dictionaries
        calls_list = []
        for call in landline_calls:
            calls_list.append({
                "ID": call.id,  # Use lowercase 'id' instead of 'ID'
                "Date": str(call.Date),
                "LandlineNum": call.LandlineNum,
                "CallsCount": call.CallsCount
            })
        
        return JSONResponse(content={"status": "success", "data": calls_list})
    except Exception as e:
        logger.error(f"Error in debug route: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"Error: {str(e)}"}
        )
        

@app.get("/check-product-columns")
async def check_product_columns(request: Request, db: Session = Depends(get_db)):
    try:
        # Get column names directly from SQL Server
        result = db.execute(text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Product'")).fetchall()
        column_names = [row[0] for row in result]
        
        # Get a sample row
        sample_row = db.execute("SELECT TOP 1 * FROM Product").first()
        sample_data = {}
        if sample_row:
            for i, col in enumerate(column_names):
                sample_data[col] = sample_row[i]
        
        return {
            "column_names": column_names,
            "sample_data": sample_data
        }
    except Exception as e:
        return {"error": str(e)}

@app.get("/sections", response_class=HTMLResponse)
async def sections_page(
    request: Request, 
    response: Response,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanView:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to view this page", "current_user": current_user}
        )
    
    # Add cache control headers to prevent back button access after logout
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    
    # Get sections (cached)
    sections = get_sections_cached(get_db)
    
    # Optimized query with joins and specific column selection
    transactions_query = (
        db.query(
            SectionTKTS.ID,
            SectionTKTS.Date,
            Section.Section,
            SectionTKTS.Num_TKTS,
            SectionTKTS.Sec_ID
        )
        .join(Section, SectionTKTS.Sec_ID == Section.SectionID)
        .order_by(SectionTKTS.Date.desc())
    )
    
    transactions = transactions_query.all()
    
    return templates.TemplateResponse(
        "sections.html", 
        {
            "request": request, 
            "transactions": transactions, 
            "sections": sections,
            "current_user": current_user,
            "active_tab": "sections"
        }
    )

@app.post("/sections/submit")
async def submit_section_form(
    request: Request, 
    date: date = Form(...), 
    section_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanAdd:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to add records"}
        )
    
    try:
        new_record = SectionTKTS(Date=date, Sec_ID=section_id, Num_TKTS=num_tkts)
        db.add(new_record)
        db.commit()
        
        # Clear cache after data change
        get_sections_cached.cache_clear()
        
        return RedirectResponse(url="/sections", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error submitting form: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/sections/update")
async def update_section_transaction(
    request: Request, 
    id: int = Form(...), 
    date: date = Form(...), 
    section_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanEdit:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to edit records"}
        )
    
    try:
        transaction = db.query(SectionTKTS).filter(SectionTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        transaction.Date = date
        transaction.Sec_ID = section_id
        transaction.Num_TKTS = num_tkts
        
        db.commit()
        return RedirectResponse(url="/sections", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating transaction: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/sections/delete")
async def delete_section_transaction(
    request: Request, 
    id: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.CanDelete:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to delete records", "current_user": current_user}
        )
    
    try:
        transaction = db.query(SectionTKTS).filter(SectionTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        db.delete(transaction)
        db.commit()
        
        return RedirectResponse(url="/sections", status_code=status.HTTP_303_SEE_OTHER)
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting transaction: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e), "current_user": current_user}
        )

# Section management routes
@app.get("/manage-sections", response_class=HTMLResponse)
async def manage_sections_page(
    request: Request, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.IsAdmin:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to manage sections", "current_user": current_user}
        )
    
    try:
        sections = db.query(Section).all()
        
        return templates.TemplateResponse(
            "manage_sections.html", 
            {
                "request": request, 
                "sections": sections,
                "current_user": current_user,
                "active_tab": "sections"
            }
        )
    except Exception as e:
        logger.error(f"Error in manage_sections_page: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": f"An error occurred: {str(e)}", "current_user": current_user}
        )

@app.post("/sections/create")
async def create_section(
    request: Request,
    section_id: int = Form(...),
    section_name: str = Form(...),
    description: str = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user or not current_user.IsAdmin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    try:
        # Check if section ID already exists
        existing_section = db.query(Section).filter(Section.SectionID == section_id).first()
        if existing_section:
            return templates.TemplateResponse(
                "error.html", 
                {"request": request, "error": "Section ID already exists"}
            )
        
        new_section = Section(
            SectionID=section_id,
            Section=section_name,
            Description=description
        )
        
        db.add(new_section)
        db.commit()
        
        # Clear cache after data change
        get_sections_cached.cache_clear()
        
        return RedirectResponse(url="/manage-sections", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating section: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/sections/update-section")
async def update_section(
    request: Request,
    section_id: int = Form(...),
    section_name: str = Form(...),
    description: str = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user or not current_user.IsAdmin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    try:
        section = db.query(Section).filter(Section.SectionID == section_id).first()
        if not section:
            raise HTTPException(status_code=404, detail="Section not found")
        
        section.Section = section_name
        section.Description = description
        
        db.commit()
        
        # Clear cache after data change
        get_sections_cached.cache_clear()
        
        return RedirectResponse(url="/manage-sections", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating section: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/sections/delete-section")
async def delete_section(
    request: Request,
    section_id: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user or not current_user.IsAdmin:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    try:
        # Check if section has associated transactions
        transactions = db.query(SectionTKTS).filter(SectionTKTS.Sec_ID == section_id).first()
        if transactions:
            return templates.TemplateResponse(
                "error.html", 
                {"request": request, "error": "Cannot delete section with associated transactions"}
            )
        
        section = db.query(Section).filter(Section.SectionID == section_id).first()
        if not section:
            raise HTTPException(status_code=404, detail="Section not found")
        
        db.delete(section)
        db.commit()
        
        # Clear cache after data change
        get_sections_cached.cache_clear()
        
        return RedirectResponse(url="/manage-sections", status_code=303)
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting section: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.get("/check-section-table")
async def check_section_table(request: Request, db: Session = Depends(get_db)):
    try:
        # Check if Section table exists
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if "Section" in tables:
            # Get column information for the Section table
            columns = inspector.get_columns('Section')
            column_names = [column['name'] for column in columns]
            
            # Get primary key information
            pk = inspector.get_pk_constraint('Section')
            
            # Get a sample row
            sample_row = db.query(Section).first()
            sample_data = {}
            if sample_row:
                sample_data = {
                    "SectionID": sample_row.SectionID,
                    "Section": sample_row.Section,
                    "Description": sample_row.Description
                }
            
            return {
                "table_exists": True,
                "columns": column_names,
                "primary_key": pk['constrained_columns'] if pk else None,
                "sample_data": sample_data
            }
        else:
            return {"table_exists": False}
    except Exception as e:
        return {"error": str(e)}

@app.get("/status", response_class=HTMLResponse)
async def status_page(
    request: Request, 
    response: Response,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.CanView:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to view this page", "current_user": current_user}
        )
    
    # Add cache control headers to prevent back button access after logout
    response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    
    try:
        # Get statuses (cached)
        statuses = get_statuses_cached(get_db)
        
        # Optimized query with joins and specific column selection
        transactions = (
            db.query(
                StatusTKTS.ID,
                StatusTKTS.Date,
                Status.Status_Name,
                StatusTKTS.Num_TKTS,
                StatusTKTS.Status_ID
            )
            .join(Status, StatusTKTS.Status_ID == Status.Status_ID)
            .order_by(StatusTKTS.Date.desc())
            .all()
        )
        
        return templates.TemplateResponse(
            "status.html", 
            {
                "request": request, 
                "transactions": transactions, 
                "statuses": statuses,
                "current_user": current_user,
                "active_tab": "status"
            }
        )
    except Exception as e:
        logger.error(f"Error in status_page: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": f"An error occurred: {str(e)}", "current_user": current_user}
        )

@app.post("/status/submit")
async def submit_status_form(
    request: Request, 
    date: date = Form(...), 
    status_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.CanAdd:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to add records"}
        )
    
    try:
        new_record = StatusTKTS(Date=date, Status_ID=status_id, Num_TKTS=num_tkts)
        db.add(new_record)
        db.commit()
        
        # Clear cache after data change
        get_statuses_cached.cache_clear()
        
        return RedirectResponse(url="/status", status_code=status.HTTP_303_SEE_OTHER)
    except Exception as e:
        db.rollback()
        logger.error(f"Error submitting form: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/status/update")
async def update_status_transaction(
    request: Request, 
    id: int = Form(...), 
    date: date = Form(...), 
    status_id: int = Form(...), 
    num_tkts: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.CanEdit:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to edit records"}
        )
    
    try:
        transaction = db.query(StatusTKTS).filter(StatusTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        transaction.Date = date
        transaction.Status_ID = status_id
        transaction.Num_TKTS = num_tkts
        
        db.commit()
        return RedirectResponse(url="/status", status_code=status.HTTP_303_SEE_OTHER)
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating transaction: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/status/delete")
async def delete_status_transaction(
    request: Request, 
    id: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.CanDelete:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to delete records"}
        )
    
    try:
        transaction = db.query(StatusTKTS).filter(StatusTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        db.delete(transaction)
        db.commit()
        return {"success": True}
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting transaction: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.get("/change-password", response_class=HTMLResponse)
async def change_password_page(request: Request):
    # Log that this route was accessed
    logger.info("Change password page accessed")
    return templates.TemplateResponse("change_password.html", {"request": request})

@app.post("/change-password")
async def change_password(
    request: Request,
    email: str = Form(...),
    current_password: str = Form(...),
    new_password: str = Form(...),
    confirm_password: str = Form(...),
    db: Session = Depends(get_db)
):
    # Log that this route was accessed
    logger.info(f"Change password form submitted for email: {email}")
    
    # Validate passwords match
    if new_password != confirm_password:
        return templates.TemplateResponse(
            "change_password.html", 
            {"request": request, "error": "New passwords do not match"}
        )
    
    # Get user by email
    user = db.query(User).filter(User.Email == email).first()
    if not user:
        logger.warning(f"Change password attempt for non-existent email: {email}")
        return templates.TemplateResponse(
            "change_password.html", 
            {"request": request, "error": "Email not found"}
        )
    
    # Verify current password
    if not verify_password(current_password, user.Password):
        logger.warning(f"Change password attempt with incorrect password for email: {email}")
        return templates.TemplateResponse(
            "change_password.html", 
            {"request": request, "error": "Current password is incorrect"}
        )
    
    # Update password
    user.Password = get_password_hash(new_password)
    db.commit()
    
    # Log the password change
    logger.info(f"Password changed successfully for user: {user.UserName}")
    
    return templates.TemplateResponse(
        "change_password.html", 
        {"request": request, "success": "Password has been changed successfully"}
    )
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.CanDelete:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to delete records"}
        )
    
    try:
        transaction = db.query(StatusTKTS).filter(StatusTKTS.ID == id).first()
        if not transaction:
            raise HTTPException(status_code=404, detail="Transaction not found")
        
        db.delete(transaction)
        db.commit()
        return RedirectResponse(url="/status", status_code=status.HTTP_303_SEE_OTHER)
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting transaction: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.get("/manage-status", response_class=HTMLResponse)
async def manage_status_page(
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.IsAdmin:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to manage statuses"}
        )
    
    statuses = db.query(Status).all()
    
    return templates.TemplateResponse(
        "manage_status.html", 
        {
            "request": request, 
            "statuses": statuses,
            "current_user": current_user,
            "active_tab": "status"
        }
    )

@app.post("/status/create")
async def create_status(
    request: Request,
    status_id: int = Form(...),
    status_name: str = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.IsAdmin:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to create statuses"}
        )
    
    try:
        # Check if status ID already exists
        existing_status = db.query(Status).filter(Status.Status_ID == status_id).first()
        if existing_status:
            return templates.TemplateResponse(
                "error.html", 
                {"request": request, "error": "Status ID already exists"}
            )
        
        new_status = Status(
            Status_ID=status_id,
            Status_Name=status_name
        )
        
        db.add(new_status)
        db.commit()
        
        # Clear cache after data change
        get_statuses_cached.cache_clear()
        
        return RedirectResponse(url="/manage-status", status_code=status.HTTP_303_SEE_OTHER)
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating status: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.post("/status/update-status")
async def update_status(
    request: Request,
    status_id: int = Form(...),
    status_name: str = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.IsAdmin:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to update statuses"}
        )
    
    try:
        status = db.query(Status).filter(Status.Status_ID == status_id).first()
        if not status:
            return templates.TemplateResponse(
                "error.html", 
                {"request": request, "error": "Status not found"}
            )
        
        status.Status_Name = status_name
        db.commit()
        
        # Clear cache after data change
        get_statuses_cached.cache_clear()
        
        return RedirectResponse(url="/manage-status", status_code=status.HTTP_303_SEE_OTHER)
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating status: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

@app.delete("/status/delete/{status_id}")
async def delete_status(
    status_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=status.HTTP_303_SEE_OTHER)
    
    if not current_user.IsAdmin:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to delete statuses"}
        )
    
    try:
        status = db.query(Status).filter(Status.Status_ID == status_id).first()
        if not status:
            return templates.TemplateResponse(
                "error.html", 
                {"request": request, "error": "Status not found"}
            )
        
        db.delete(status)
        db.commit()
        
        # Clear cache after data change
        get_statuses_cached.cache_clear()
        
        return RedirectResponse(url="/manage-status", status_code=status.HTTP_303_SEE_OTHER)
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting status: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": str(e)}
        )

# Add this route to check and create Landlines tables if needed
@app.get("/check-landlines-table")
async def check_landlines_table(request: Request, db: Session = Depends(get_db)):
    try:
        # Check if Landlines table exists
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if "Landlines" not in tables:
            # Create Landlines table
            Base.metadata.tables["Landlines"].create(engine)
            logger.info("Created Landlines table")
            
            # Add some sample landlines
            sample_landlines = [
                Landline(Landline="Main Office"),
                Landline(Landline="Customer Support"),
                Landline(Landline="Sales Department"),
                Landline(Landline="Technical Support")
            ]
            db.add_all(sample_landlines)
            db.commit()
        
        if "LandLinesCalls" not in tables:
            # Create LandLinesCalls table
            Base.metadata.tables["LandLinesCalls"].create(engine)
            logger.info("Created LandLinesCalls table")
        
        return {"status": "success", "message": "Landlines tables checked/created"}
    except Exception as e:
        logger.error(f"Error checking/creating Landlines tables: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": f"Error checking/creating Landlines tables: {str(e)}"}
        )

@app.get("/check-landlinescalls-columns")
async def check_landlinescalls_columns(request: Request, db: Session = Depends(get_db)):
    try:
        # Get column names directly from SQL Server
        result = db.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'LandLinesCalls'").fetchall()
        column_names = [row[0] for row in result]
        
        # Get a sample row if available
        sample_row = None
        try:
            sample_row = db.execute("SELECT TOP 1 * FROM LandLinesCalls").first()
        except:
            pass
        
        sample_data = {}
        if sample_row:
            for i, col in enumerate(column_names):
                sample_data[col] = sample_row[i]
        
        return {
            "column_names": column_names,
            "sample_data": sample_data
        }
    except Exception as e:
        logger.error(f"Error checking LandLinesCalls columns: {str(e)}", exc_info=True)
        return {"error": str(e)}

@app.get("/landlines")
async def landlines_page(
    request: Request, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    try:
        # Get all landlines
        landlines = db.query(Landline).all()
        
        # Get all landline calls with join to get the landline name
        landline_calls_query = (
            db.query(
                LandlineCall.id,  # Use lowercase 'id' instead of 'ID'
                LandlineCall.Date,
                LandlineCall.LandlineNum,
                Landline.Landline,
                LandlineCall.CallsCount
            )
            .join(Landline, LandlineCall.LandlineNum == Landline.id)
            .order_by(LandlineCall.Date.desc())
        )
        
        landline_calls = landline_calls_query.all()
        
        # Convert to list of dictionaries for easier template access
        calls_list = []
        for call in landline_calls:
            calls_list.append({
                "ID": call.id,  # Use lowercase 'id' instead of 'ID'
                "Date": call.Date,
                "LandlineNum": call.LandlineNum,
                "Landline": call.Landline,
                "CallsCount": call.CallsCount
            })
        
        return templates.TemplateResponse(
            "landlines.html", 
            {
                "request": request, 
                "landlines": landlines, 
                "calls": calls_list,
                "current_user": current_user
            }
        )
    except Exception as e:
        logger.error(f"Error loading landlines page: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": f"An error occurred: {str(e)}", "current_user": current_user}
        )

    except Exception as e:
        logger.error(f"Error loading landlines page: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": f"An error occurred: {str(e)}", "current_user": current_user}
        )

@app.get("/check-userlogs-table")
async def check_userlogs_table(request: Request, db: Session = Depends(get_db)):
    try:
        # Check if UserLogs table exists
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if "UserLogs" not in tables:
            # Create UserLogs table
            Base.metadata.tables["UserLogs"].create(engine)
            logger.info("Created UserLogs table")
        
        return {"status": "success", "message": "UserLogs table checked/created"}
    except Exception as e:
        logger.error(f"Error checking/creating UserLogs table: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": f"Error checking/creating UserLogs table: {str(e)}"}
        )

@app.post("/landlines/submit")
async def submit_landline_call(
    request: Request, 
    date: date = Form(...), 
    landline_id: int = Form(...), 
    calls_count: int = Form(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return RedirectResponse(url="/login", status_code=303)
    
    if not current_user.CanAdd:
        return templates.TemplateResponse(
            "error.html", 
            {"request": request, "error": "You don't have permission to add records", "current_user": current_user}
        )
    
    try:
        # Get the landline object first to verify it exists
        landline = db.query(Landline).filter(Landline.id == landline_id).first()
        if not landline:
            raise HTTPException(status_code=404, detail=f"Landline with ID {landline_id} not found")
        
        # Convert date string to datetime object if needed
        call_date = datetime.combine(date, datetime.min.time())
        
        # Create new landline call record
        new_call = LandlineCall(
            Date=call_date,
            LandlineNum=landline_id,
            CallsCount=calls_count
        )
        
        db.add(new_call)
        
        # Check if UserLogs table exists
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if "UserLogs" in tables:
            # Add to user logs
            log = UserLog(
                Date=datetime.now(),
                UserID=current_user.ID,
                UserName=current_user.Username,
                Type="Add",
                Name=f"Landline: {landline.Landline}",
                Num_TKTS=calls_count
            )
            db.add(log)
        
        db.commit()
        
        return RedirectResponse(url="/landlines", status_code=303)
    except HTTPException as he:
        db.rollback()
        logger.error(f"HTTP error adding landline call: {str(he)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {"request": request, "error": he.detail, "current_user": current_user}
        )
    except Exception as e:
        db.rollback()
        logger.error(f"Error adding landline call: {str(e)}", exc_info=True)
        return templates.TemplateResponse(
            "error.html",
            {"request": request, "error": f"An error occurred: {str(e)}", "current_user": current_user}
        )

@app.put("/landlines/update/{record_id}")
async def update_landline_call(
    record_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return JSONResponse(status_code=401, content={"status": "error", "message": "Not authenticated"})
    
    if not current_user.CanEdit:
        return JSONResponse(status_code=403, content={"status": "error", "message": "You don't have permission to edit records"})
    
    try:
        # Parse form data
        form_data = await request.form()
        date_str = form_data.get('date')
        landline_id = int(form_data.get('landline_id'))
        calls_count = int(form_data.get('calls_count'))
        
        # Convert date string to date object
        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
        
        # Find the record to update using the ID
        call = db.query(LandlineCall).filter(
            LandlineCall.id == record_id  # Use lowercase 'id' instead of 'ID'
        ).first()
        
        if not call:
            return JSONResponse(status_code=404, content={"status": "error", "message": f"Record not found with ID {record_id}"})
        
        # Update the record
        call.Date = datetime.combine(date_obj, datetime.min.time())
        call.LandlineNum = landline_id
        call.CallsCount = calls_count
        
        db.commit()
        
        return JSONResponse(content={"status": "success", "message": "Record updated successfully"})
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating landline call: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500, 
            content={"status": "error", "message": f"Error: {str(e)}"}
        )

@app.delete("/landlines/delete/{record_id}")
async def delete_landline_call(
    record_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return JSONResponse(status_code=401, content={"status": "error", "message": "Not authenticated"})
    
    if not current_user.CanDelete:
        return JSONResponse(status_code=403, content={"status": "error", "message": "You don't have permission to delete records"})
    
    try:
        # Find the record to delete using the ID
        call = db.query(LandlineCall).filter(
            LandlineCall.id == record_id  # Use lowercase 'id' instead of 'ID'
        ).first()
        
        if not call:
            return JSONResponse(status_code=404, content={"status": "error", "message": f"Record not found with ID {record_id}"})
        
        # Delete the record
        db.delete(call)
        db.commit()
        
        return JSONResponse(content={"status": "success", "message": "Record deleted successfully"})
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting landline call: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500, 
            content={"status": "error", "message": f"Error: {str(e)}"}
        )

@app.delete("/landlines/delete/{record_id}")
async def delete_landline_call(
    record_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user:
        return JSONResponse(status_code=401, content={"status": "error", "message": "Not authenticated"})
    
    if not current_user.CanDelete:
        return JSONResponse(status_code=403, content={"status": "error", "message": "You don't have permission to delete records"})
    
    try:
        # Find the record to delete using the ID
        call = db.query(LandlineCall).filter(
            LandlineCall.ID == record_id
        ).first()
        
        if not call:
            return JSONResponse(status_code=404, content={"status": "error", "message": f"Record not found with ID {record_id}"})
        
        # Get landline name for logging
        landline = db.query(Landline).filter(Landline.id == call.LandlineNum).first()
        landline_name = landline.Landline if landline else "Unknown"
        calls_count = call.CallsCount
        
        # Delete the record
        db.delete(call)
        
        # Check if UserLogs table exists
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        if "UserLogs" in tables:
            # Add to user logs
            log = UserLog(
                Date=datetime.now(),
                UserID=current_user.ID,
                UserName=current_user.Username,
                Type="Delete",
                Name=f"Landline: {landline_name}",
                Num_TKTS=calls_count
            )
            db.add(log)
        
        db.commit()
        
        return JSONResponse(content={"status": "success", "message": "Record deleted successfully"})
    except Exception as e:
        db.rollback()
        logger.error(f"Error deleting landline call: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500, 
            content={"status": "error", "message": f"Error: {str(e)}"}
        )

##### Incase of Hanging when load Browser #####
#if __name__ == "__main__":
    #app.run(debug=True)


if __name__ == "__main__":
    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)

















































