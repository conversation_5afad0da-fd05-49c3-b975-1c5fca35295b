body {
    background-color: #f5f5f5;
    padding-top: 60px;
    padding-bottom: 50px;
}

.container {
    max-width: 1200px;
}

.card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    font-weight: bold;
}

.table {
    font-size: 0.9rem;
}

.table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

th {
    cursor: pointer;
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 1;
}

.sort-icon {
    font-size: 0.8rem;
    margin-left: 5px;
}

.sort-icon.active {
    color: #0d6efd;
}

/* Fixed header and footer styles */
.fixed-top {
    z-index: 1030;
}

.fixed-bottom {
    z-index: 1030;
    height: 40px;
}

/* Adjust main content for fixed header and footer */
main {
    padding-top: 20px;
    padding-bottom: 60px;
}

/* Print styles */
@media print {
    .card-header, .btn, input, select, label, .form-control, .card:first-child, .card:nth-child(2) {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .card-body {
        padding: 0 !important;
    }
    
    .table-responsive {
        overflow: visible !important;
        max-height: none !important;
    }
    
    .container {
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 !important;
        margin: 0 !important;
    }
    
    .fixed-top, .fixed-bottom {
        display: none !important;
    }
    
    body {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}

#searchInput {
    max-width: 300px;
}

/* Add fixed container styles */
.container-fixed {
    position: fixed;
    top: 60px; /* Adjust based on your header height */
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 1200px; /* Match your container max-width */
    z-index: 1000;
    background-color: #f5f5f5; /* Match your body background */
    padding: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Add padding to the body to prevent content from being hidden behind the fixed container */
body.has-fixed-container {
    padding-top: 120px; /* Adjust based on your fixed container height + header height */
}

/* Adjust for smaller screens */
@media (max-width: 768px) {
    .container-fixed {
        width: 100%;
        padding: 10px;
    }
    
    body.has-fixed-container {
        padding-top: 150px; /* Increase for smaller screens where content might wrap */
    }
}

