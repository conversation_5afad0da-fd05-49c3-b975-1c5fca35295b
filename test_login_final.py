#!/usr/bin/env python3
"""
Final test to verify login functionality
"""

import requests
import time

def test_login():
    print("Testing login functionality...")
    
    # First test if server is responding
    try:
        print("1. Testing server connectivity...")
        response = requests.get("http://localhost:8000/login", timeout=5)
        print(f"   GET /login status: {response.status_code}")
        
        if response.status_code != 200:
            print(f"   ❌ Server not responding properly")
            return False
            
        print("   ✅ Server is responding")
        
        # Now test login
        print("2. Testing login...")
        login_data = {
            "email": "<EMAIL>",
            "password": "admin123"
        }
        
        response = requests.post("http://localhost:8000/login", data=login_data, allow_redirects=False, timeout=10)
        print(f"   POST /login status: {response.status_code}")
        
        if response.status_code == 303:
            print("   ✅ Login successful - received redirect")
            
            # Check for token cookie
            cookies = response.headers.get('Set-Cookie', '')
            if 'token=' in cookies:
                print("   ✅ Token cookie set successfully")
                print("   🎉 JWT authentication is working!")
                return True
            else:
                print("   ❌ No token cookie found")
                return False
        elif response.status_code == 200:
            # Check if there's an error message in the response
            if 'error' in response.text.lower():
                print("   ❌ Login failed - error in response")
                print(f"   Response snippet: {response.text[:200]}")
                return False
            else:
                print("   ⚠️  Unexpected 200 response")
                return False
        else:
            print(f"   ❌ Unexpected status code: {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_login()
    if success:
        print("\n🎉 All tests passed! JWT authentication is working correctly.")
    else:
        print("\n❌ Tests failed. Check the server logs for errors.")
