{% extends "base.html" %}

{% block title %}User Transaction Logs{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">User Transaction Logs</h1>
        <div>
            <button class="btn btn-sm btn-primary shadow-sm" id="printBtn">
                <i class="bi bi-printer me-1"></i> Print Report
            </button>
            <button class="btn btn-sm btn-success shadow-sm" id="exportBtn">
                <i class="bi bi-file-excel me-1"></i> Export to Excel
            </button>
        </div>
    </div>

    <!-- Filter Card -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h2>Filter Options</h2>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="startDate" class="form-label">Start Date</label>
                    <input type="date" class="form-control" id="startDate">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="endDate" class="form-label">End Date</label>
                    <input type="date" class="form-control" id="endDate">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="typeFilter" class="form-label">Type</label>
                    <select class="form-select" id="typeFilter">
                        <option value="">All Types</option>
                        <option value="Channel">Channel</option>
                        <option value="Employee">Employee</option>
                        <option value="Product">Product</option>
                        <option value="Section">Section</option>
                        <option value="Status">Status</option>
                        <option value="Landline">Landline</option>
                        <option value="User">User</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="searchInput" class="form-label">Search</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Search...">
                </div>
            </div>
            <div class="text-end">
                <button type="button" class="btn btn-primary" id="filterBtn">
                    <i class="bi bi-filter me-1"></i>Apply Filter
                </button>
            </div>
        </div>
    </div>

    <!-- Logs Table -->
    <div class="card">
        <div class="card-header bg-secondary text-white">
            <h2>Transaction Logs</h2>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="logsTable">
                    <thead>
                        <tr>
                            <th data-sort="id">ID</th>
                            <th data-sort="date">Date</th>
                            <th data-sort="username">Username</th>
                            <th data-sort="type">Type</th>
                            <th data-sort="name">Name</th>
                            <th data-sort="tkts">TKTS</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in logs %}
                        <tr>
                            <td>{{ log.ID }}</td>
                            <td>{{ log.Date.strftime('%Y-%m-%d') }}</td>
                            <td>{{ log.UserName }}</td>
                            <td>{{ log.Type }}</td>
                            <td>{{ log.Name }}</td>
                            <td>{{ log.Num_TKTS }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-light">
            <strong>Total Records: <span id="recordCount">{{ logs|length }}</span></strong>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const table = document.getElementById('logsTable');
        const recordCount = document.getElementById('recordCount');
        const searchInput = document.getElementById('searchInput');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        const typeFilter = document.getElementById('typeFilter');
        const filterBtn = document.getElementById('filterBtn');
        const printBtn = document.getElementById('printBtn');
        const exportBtn = document.getElementById('exportBtn');
        
        // Find the latest date in the table
        let latestDate = new Date().toISOString().split('T')[0]; // Default to today
        if (table) {
            const dateColumn = 1; // Date is in the second column (index 1)
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 0) {
                // Get all dates from the table
                const dates = Array.from(rows).map(row => new Date(row.cells[dateColumn].textContent));
                // Find the latest date
                if (dates.length > 0) {
                    const maxDate = new Date(Math.max.apply(null, dates));
                    latestDate = maxDate.toISOString().split('T')[0];
                }
            }
        }
        
        // Set date inputs to latest date
        if (startDateInput) startDateInput.value = latestDate;
        if (endDateInput) endDateInput.value = latestDate;
        
        // Filter functionality
        function filterTable() {
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const startDate = startDateInput && startDateInput.value ? new Date(startDateInput.value) : null;
            const endDate = endDateInput && endDateInput.value ? new Date(endDateInput.value) : null;
            const typeValue = typeFilter ? typeFilter.value.toLowerCase() : '';
            
            if (!table) return;
            
            const rows = table.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                const dateCell = row.cells[1].textContent;
                const rowDate = new Date(dateCell);
                const typeCell = row.cells[3].textContent.toLowerCase();
                
                let dateMatch = true;
                if (startDate && endDate) {
                    // Set end date to end of day for inclusive comparison
                    const endOfDay = new Date(endDate);
                    endOfDay.setHours(23, 59, 59, 999);
                    dateMatch = rowDate >= startDate && rowDate <= endOfDay;
                }
                
                let typeMatch = true;
                if (typeValue) {
                    typeMatch = typeCell === typeValue;
                }
                
                if (text.includes(searchTerm) && dateMatch && typeMatch) {
                    row.style.display = '';
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                }
            });
            
            if (recordCount) {
                recordCount.textContent = visibleCount;
            }
        }
        
        // Event listeners
        if (searchInput) {
            searchInput.addEventListener('keyup', filterTable);
        }
        
        if (filterBtn) {
            filterBtn.addEventListener('click', filterTable);
        }
        
        // Print functionality
        if (printBtn) {
            printBtn.addEventListener('click', function() {
                window.print();
            });
        }
        
        // Export to Excel functionality
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                if (!table) return;
                
                // Get visible rows only
                const visibleRows = Array.from(table.querySelectorAll('tbody tr'))
                    .filter(row => row.style.display !== 'none');
                
                // Create worksheet data
                const wsData = [
                    ['ID', 'Date', 'Username', 'Type', 'Name', 'TKTS'] // Header row
                ];
                
                visibleRows.forEach(row => {
                    const rowData = [];
                    Array.from(row.cells).forEach(cell => {
                        rowData.push(cell.textContent);
                    });
                    wsData.push(rowData);
                });
                
                // Create workbook and worksheet
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(wsData);
                
                // Add worksheet to workbook
                XLSX.utils.book_append_sheet(wb, ws, 'User Transaction Logs');
                
                // Generate filename with current date
                const now = new Date();
                const filename = `User_Transaction_Logs_${now.toISOString().split('T')[0]}.xlsx`;
                
                // Export to file
                XLSX.writeFile(wb, filename);
            });
        }
        
        // Sort functionality
        const sortHeaders = document.querySelectorAll('th[data-sort]');
        let currentSort = { column: 'date', direction: 'desc' };
        
        function sortTable(column) {
            if (!table) return;
            
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            // Determine sort direction
            let direction = 'asc';
            if (currentSort.column === column) {
                direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            }
            
            // Update current sort
            currentSort = { column, direction };
            
            // Get column index
            const headerRow = table.querySelector('thead tr');
            const headers = Array.from(headerRow.querySelectorAll('th'));
            const columnIndex = headers.findIndex(th => th.getAttribute('data-sort') === column);
            
            if (columnIndex === -1) return;
            
            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent;
                let bValue = b.cells[columnIndex].textContent;
                
                // Handle date sorting
                if (column === 'date') {
                    aValue = new Date(aValue);
                    bValue = new Date(bValue);
                }
                // Handle numeric sorting
                else if (column === 'id' || column === 'tkts') {
                    aValue = parseInt(aValue);
                    bValue = parseInt(bValue);
                }
                
                if (aValue < bValue) return direction === 'asc' ? -1 : 1;
                if (aValue > bValue) return direction === 'asc' ? 1 : -1;
                return 0;
            });
            
            // Reorder table
            rows.forEach(row => tbody.appendChild(row));
        }
        
        if (sortHeaders) {
            sortHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const column = this.getAttribute('data-sort');
                    sortTable(column);
                });
            });
        }
        
        // Initial sort by date (newest first)
        sortTable('date');
        
        // Initial filter
        filterTable();
    });
</script>
{% endblock %}

