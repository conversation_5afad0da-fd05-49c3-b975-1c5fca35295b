-- Add password and permission columns to Users table
ALTER TABLE [dbo].[Users]
ADD [Password] [nvarchar](255) NULL,
    [IsActive] [bit] NOT NULL DEFAULT 1,
    [IsAdmin] [bit] NOT NULL DEFAULT 0,
    [CanView] [bit] NOT NULL DEFAULT 1,
    [CanAdd] [bit] NOT NULL DEFAULT 0,
    [CanEdit] [bit] NOT NULL DEFAULT 0,
    [CanDelete] [bit] NOT NULL DEFAULT 0,
    [ResetToken] [nvarchar](255) NULL,
    [ResetTokenExpiry] [datetime] NULL;
GO

-- Update existing users to have default password (you should change this after first login)
UPDATE [dbo].[Users]
SET [Password] = 'pbkdf2:sha256:150000$' + CONVERT(nvarchar(255), NEWID())
WHERE [Password] IS NULL;
GO