{% extends "base.html" %}

{% block title %}Product TKTS Form - Comsys Software PMO & CSO System{% endblock %}

{% block content %}
<!-- Form Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h2>Add New Product TKTs</h2>
    </div>
    <div class="card-body">
        <form action="/products/submit" method="post">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="date" name="date" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="product_id" class="form-label">Product</label>
                    <select class="form-select" id="product_id" name="product_id" required>
                        <option value="" selected disabled>Select Product</option>
                        {% for product in products %}
                        <option value="{{ product.ProductID }}">{{ product.Name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="num_tkts" class="form-label">Number of TKTs</label>
                    <input type="number" class="form-control" id="num_tkts" name="num_tkts" min="0" required>
                </div>
            </div>
            <div class="text-end">
                <button type="submit" class="btn btn-primary" {% if not current_user.CanAdd %}disabled{% endif %}>
                    <i class="bi bi-plus-circle me-2"></i>Add Transaction
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Filter Card -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white">
        <h2>Filter & Export</h2>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4 mb-3">
                <label for="searchInput" class="form-label">Search:</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="Search...">
                    <button class="btn btn-primary" type="button" id="searchButton">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <label class="form-label">Date Range:</label>
                <div class="input-group">
                    <input type="date" class="form-control" id="startDate">
                    <span class="input-group-text">to</span>
                    <input type="date" class="form-control" id="endDate">
                    <button class="btn btn-primary" type="button" id="filterDateBtn">
                        <i class="bi bi-funnel"></i> Filter
                    </button>
                </div>
            </div>
            <div class="col-md-2 mb-3">
                <label for="productFilter" class="form-label">Product:</label>
                <select class="form-select" id="productFilter">
                    <option value="">All Products</option>
                    {% for product in products %}
                    <option value="{{ product.Name|lower }}">{{ product.Name }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12 text-end">
                <button class="btn btn-success" id="exportBtn">
                    <i class="bi bi-file-excel me-2"></i>Export to Excel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Grid -->
<div class="card">
    <div class="card-header bg-secondary text-white">
        <h2>Transactions History</h2>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="transactionsTable">
                <thead>
                    <tr>
                        <th data-sort="id">ID <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="date">Date <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="product">Product <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="tkts">TKTS <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.ID }}</td>
                        <td>{{ transaction.Date.strftime('%Y-%m-%d') if transaction.Date is not string else transaction.Date }}</td>
                        <td>{{ transaction.ProductName }}</td>
                        <td>{{ transaction.Num_TKTS }}</td>
                        <td>
                            <button class="btn btn-sm btn-warning edit-btn" data-id="{{ transaction.ID }}" 
                                    data-date="{{ transaction.Date.strftime('%Y-%m-%d') if transaction.Date is not string else transaction.Date }}" 
                                    data-product="{{ transaction.ProductName }}"
                                    data-product-id="{{ transaction.Pro_ID }}"
                                    data-tkts="{{ transaction.Num_TKTS }}"
                                    {% if not current_user.CanEdit %}disabled{% endif %}>
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-danger delete-btn" data-id="{{ transaction.ID }}"
                                    {% if not current_user.CanDelete %}disabled{% endif %}>
                                <i class="bi bi-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-secondary fw-bold">
                        <td colspan="3" class="text-end">Total:</td>
                        <td id="gridTotalTkts">0</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="card-footer bg-light">
        <strong>Total Records: <span id="recordCount">{{ transactions|length }}</span></strong>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editModalLabel">Edit Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" action="/products/update" method="post">
                    <input type="hidden" id="edit_id" name="id">
                    <div class="mb-3">
                        <label for="edit_date" class="form-label">Date:</label>
                        <input type="date" class="form-control" id="edit_date" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_product_id" class="form-label">Product:</label>
                        <select class="form-select" id="edit_product_id" name="product_id" required>
                            {% for product in products %}
                            <option value="{{ product.ProductID }}">{{ product.Name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_num_tkts" class="form-label">Number of TKTs:</label>
                        <input type="number" class="form-control" id="edit_num_tkts" name="num_tkts" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEditBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Delete Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this transaction? This action cannot be undone.</p>
                <form id="deleteForm" action="/products/delete" method="post">
                    <input type="hidden" id="delete_id" name="id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const searchButton = document.getElementById('searchButton');
        const table = document.getElementById('transactionsTable');
        const recordCount = document.getElementById('recordCount');
        const sortHeaders = document.querySelectorAll('th[data-sort]');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        const filterDateBtn = document.getElementById('filterDateBtn');
        const productFilter = document.getElementById('productFilter');
        const exportBtn = document.getElementById('exportBtn');
        
        let currentSort = { column: 'date', direction: 'desc' };
        
        // Find the latest transaction date
        let latestDate = new Date().toISOString().split('T')[0]; // Default to today
        if (table) {
            const dateColumn = 1; // Date is in the second column (index 1)
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 0) {
                // Get all dates from the table
                const dates = Array.from(rows).map(row => new Date(row.cells[dateColumn].textContent));
                // Find the latest date
                if (dates.length > 0) {
                    const maxDate = new Date(Math.max.apply(null, dates));
                    latestDate = maxDate.toISOString().split('T')[0];
                }
            }
        }
        
        // Set date inputs - both start and end date to latest transaction date
        if (startDateInput) {
            startDateInput.value = latestDate;
        }
        
        if (endDateInput) {
            endDateInput.value = latestDate;
        }
        
        // Set default date for new transaction to today
        const dateInput = document.getElementById('date');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0];
        }
        
        // Search functionality
        function filterTable() {
            const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
            const startDate = startDateInput && startDateInput.value ? new Date(startDateInput.value) : null;
            const endDate = endDateInput && endDateInput.value ? new Date(endDateInput.value) : null;
            const selectedProduct = productFilter ? productFilter.value.toLowerCase() : '';
            
            if (!table) return;
            
            const rows = table.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const dateCell = row.cells[1].textContent;
                const productCell = row.cells[2].textContent.toLowerCase();
                const rowDate = new Date(dateCell);
                
                let showRow = true;
                
                // Apply search filter
                if (searchTerm) {
                    showRow = false;
                    for (let i = 0; i < row.cells.length - 1; i++) {
                        if (row.cells[i].textContent.toLowerCase().includes(searchTerm)) {
                            showRow = true;
                            break;
                        }
                    }
                }
                
                // Apply date filter
                if (showRow && startDate && endDate) {
                    // Set end date to end of day for inclusive comparison
                    const endOfDay = new Date(endDate);
                    endOfDay.setHours(23, 59, 59, 999);
                    showRow = rowDate >= startDate && rowDate <= endOfDay;
                }
                
                // Apply product filter
                if (showRow && selectedProduct) {
                    showRow = productCell === selectedProduct;
                }
                
                row.style.display = showRow ? '' : 'none';
                if (showRow) visibleCount++;
            });
            
            if (recordCount) {
                recordCount.textContent = visibleCount;
            }
            
            // Update grid totals after filtering
            updateGridTotals();
        }
        
        // Add event listeners for automatic filtering
        if (startDateInput) {
            startDateInput.addEventListener('change', function() {
                console.log("Start date changed, auto-filtering");
                filterTable();
            });
        }
        
        if (endDateInput) {
            endDateInput.addEventListener('change', function() {
                console.log("End date changed, auto-filtering");
                filterTable();
            });
        }
        
        if (productFilter) {
            productFilter.addEventListener('change', function() {
                console.log("Product filter changed, auto-filtering");
                filterTable();
            });
        }
        
        if (searchInput) {
            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    console.log("Search input enter pressed, filtering");
                    filterTable();
                }
            });
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', function() {
                console.log("Search button clicked, filtering");
                filterTable();
            });
        }
        
        if (filterDateBtn) {
            filterDateBtn.addEventListener('click', function() {
                console.log("Filter button clicked, filtering");
                filterTable();
            });
        }
        
        // Update grid totals specifically for this page
        function updateGridTotals() {
            console.log("Updating grid totals");
            const table = document.getElementById('transactionsTable');
            if (!table) return;
            
            const rows = Array.from(table.querySelectorAll('tbody tr'))
                .filter(row => row.style.display !== 'none');
            
            // Calculate total TKTs
            let totalTkts = 0;
            rows.forEach(row => {
                if (row.cells.length > 3) {
                    const tktsValue = row.cells[3].textContent.trim();
                    if (tktsValue && !isNaN(tktsValue)) {
                        totalTkts += parseInt(tktsValue);
                    }
                }
            });
            
            // Update grid total element
            const gridTotalTkts = document.getElementById('gridTotalTkts');
            if (gridTotalTkts) {
                gridTotalTkts.textContent = totalTkts;
                console.log("Grid total updated to:", totalTkts);
            }
        }
        
        // Initial update of grid totals
        setTimeout(updateGridTotals, 500);
        
        // Trigger initial filter to update totals
        setTimeout(function() {
            console.log("Initial filter triggered");
            filterTable();
        }, 600);
        
        // Sorting functionality
        sortHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const column = this.getAttribute('data-sort');
                
                // Toggle sort direction if clicking the same column
                if (currentSort.column === column) {
                    currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort.column = column;
                    currentSort.direction = 'asc';
                }
                
                sortTable(column, currentSort.direction);
                
                // Update sort icons
                sortHeaders.forEach(h => {
                    h.querySelector('.sort-icon').className = 'bi bi-arrow-down-up sort-icon';
                });
                
                const icon = this.querySelector('.sort-icon');
                icon.className = `bi bi-arrow-${currentSort.direction === 'asc' ? 'up' : 'down'} sort-icon`;
            });
        });
        
        function sortTable(column, direction) {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            // Sort the rows
            rows.sort((a, b) => {
                let aValue, bValue;
                
                if (column === 'id') {
                    aValue = parseInt(a.cells[0].textContent);
                    bValue = parseInt(b.cells[0].textContent);
                } else if (column === 'date') {
                    aValue = new Date(a.cells[1].textContent);
                    bValue = new Date(b.cells[1].textContent);
                } else if (column === 'product') {
                    aValue = a.cells[2].textContent.toLowerCase();
                    bValue = b.cells[2].textContent.toLowerCase();
                } else if (column === 'tkts') {
                    aValue = parseInt(a.cells[3].textContent);
                    bValue = parseInt(b.cells[3].textContent);
                }
                
                if (direction === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });
            
            // Remove all rows from the table
            while (tbody.firstChild) {
                tbody.removeChild(tbody.firstChild);
            }
            
            // Add sorted rows back to the table
            rows.forEach(row => {
                tbody.appendChild(row);
            });
            
            // Update grid totals after sorting
            updateGridTotals();
        }
        
        // Edit transaction
        const editModal = document.getElementById('editModal');
        const editButtons = document.querySelectorAll('.edit-btn');
        const editForm = document.getElementById('editForm');
        const saveEditBtn = document.getElementById('saveEditBtn');
        
        if (editButtons) {
            editButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    const date = this.getAttribute('data-date');
                    const productId = this.getAttribute('data-product-id');
                    const tkts = this.getAttribute('data-tkts');
                    
                    document.getElementById('edit_id').value = id;
                    document.getElementById('edit_date').value = date;
                    document.getElementById('edit_product_id').value = productId;
                    document.getElementById('edit_num_tkts').value = tkts;
                    
                    const modal = new bootstrap.Modal(editModal);
                    modal.show();
                });
            });
        }
        
        if (saveEditBtn) {
            saveEditBtn.addEventListener('click', function() {
                editForm.submit();
            });
        }
        
        // Delete transaction
        const deleteModal = document.getElementById('deleteModal') ? new bootstrap.Modal(document.getElementById('deleteModal')) : null;
        const deleteButtons = document.querySelectorAll('.delete-btn');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');

        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                if (deleteModal) {
                    document.getElementById('delete_id').value = id;
                    deleteModal.show();
                }
            });
        });

        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', function() {
                const id = document.getElementById('delete_id').value;
                
                fetch(`/products/delete/${id}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (response.ok) {
                        // Refresh the page to show updated data
                        window.location.reload();
                    } else {
                        return response.json().then(data => {
                            throw new Error(data.detail || 'Error deleting transaction');
                        });
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            });
        }
        
        // Initial call to update grid totals
        updateGridTotals();
    });
</script>
{% endblock %}











