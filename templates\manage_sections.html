{% extends "base.html" %}

{% block title %}Manage Sections - Comsys Software PMO & CSO System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2>Manage Sections</h2>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h4>Add New Section</h4>
                            </div>
                            <div class="card-body">
                                <form action="/sections/create-section" method="post">
                                    <div class="mb-3">
                                        <label for="section_id" class="form-label">Section ID</label>
                                        <input type="number" class="form-control" id="section_id" name="section_id" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="section_name" class="form-label">Section Name</label>
                                        <input type="text" class="form-control" id="section_name" name="section_name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <input type="text" class="form-control" id="description" name="description">
                                    </div>
                                    <div class="text-end">
                                        <button type="submit" class="btn btn-success">
                                            <i class="bi bi-plus-circle me-2"></i>Add Section
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Section ID</th>
                                <th>Section Name</th>
                                <th>Description</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for section in sections %}
                            <tr>
                                <td>{{ section.SectionID }}</td>
                                <td>{{ section.Section }}</td>
                                <td>{{ section.Description }}</td>
                                <td>
                                    <button class="btn btn-sm btn-warning edit-section-btn" 
                                            data-section-id="{{ section.SectionID }}" 
                                            data-section-name="{{ section.Section }}"
                                            data-description="{{ section.Description }}">
                                        <i class="bi bi-pencil"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-danger delete-section-btn" 
                                            data-section-id="{{ section.SectionID }}">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="mt-3">
                    <a href="/sections" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Section Transactions
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Section Modal -->
<div class="modal fade" id="editSectionModal" tabindex="-1" aria-labelledby="editSectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editSectionModalLabel">Edit Section</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editSectionForm" action="/sections/update-section" method="post">
                    <input type="hidden" id="edit_section_id" name="section_id">
                    <div class="mb-3">
                        <label for="edit_section_name" class="form-label">Section Name:</label>
                        <input type="text" class="form-control" id="edit_section_name" name="section_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description:</label>
                        <input type="text" class="form-control" id="edit_description" name="description">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveSectionBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Section Modal -->
<div class="modal fade" id="deleteSectionModal" tabindex="-1" aria-labelledby="deleteSectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteSectionModalLabel">Delete Section</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this section? This action cannot be undone.</p>
                <p class="text-danger"><strong>Note:</strong> You cannot delete a section that has transactions. Delete all transactions for this section first.</p>
                <form id="deleteSectionForm" action="/sections/delete-section" method="post">
                    <input type="hidden" id="delete_section_id" name="section_id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteSectionBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Edit section
        const editSectionModal = document.getElementById('editSectionModal');
        const editSectionBtns = document.querySelectorAll('.edit-section-btn');
        const editSectionForm = document.getElementById('editSectionForm');
        const saveSectionBtn = document.getElementById('saveSectionBtn');
        
        if (editSectionBtns) {
            editSectionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const sectionId = this.getAttribute('data-section-id');
                    const sectionName = this.getAttribute('data-section-name');
                    const description = this.getAttribute('data-description');
                    
                    document.getElementById('edit_section_id').value = sectionId;
                    document.getElementById('edit_section_name').value = sectionName;
                    document.getElementById('edit_description').value = description || '';
                    
                    const modal = new bootstrap.Modal(editSectionModal);
                    modal.show();
                });
            });
        }
        
        if (saveSectionBtn) {
            saveSectionBtn.addEventListener('click', function() {
                editSectionForm.submit();
            });
        }
        
        // Delete section
        const deleteSectionModal = document.getElementById('deleteSectionModal');
        const deleteSectionBtns = document.querySelectorAll('.delete-section-btn');
        const deleteSectionForm = document.getElementById('deleteSectionForm');
        const confirmDeleteSectionBtn = document.getElementById('confirmDeleteSectionBtn');
        
        if (deleteSectionBtns) {
            deleteSectionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const sectionId = this.getAttribute('data-section-id');
                    document.getElementById('delete_section_id').value = sectionId;
                    
                    const modal = new bootstrap.Modal(deleteSectionModal);
                    modal.show();
                });
            });
        }
        
        if (confirmDeleteSectionBtn) {
            confirmDeleteSectionBtn.addEventListener('click', function() {
                deleteSectionForm.submit();
            });
        }
    });
</script>
{% endblock %}
