{% extends "base.html" %}

{% block title %}Channel TKTS Form - Comsys Software PMO & CSO System{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<!-- Form Card -->
<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h2>Add New Channel TKTs</h2>
    </div>
    <div class="card-body">
        <form action="/channels/submit" method="post">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="date" class="form-label">Date</label>
                    <input type="date" class="form-control" id="date" name="date" required>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="channel_id" class="form-label">Channel</label>
                    <select class="form-select" id="channel_id" name="channel_id" required>
                        <option value="" selected disabled>Select Channel</option>
                        {% for channel in channels %}
                        <option value="{{ channel.ChannelID }}">{{ channel.Channel }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label for="num_tkts" class="form-label">Number of TKTS</label>
                    <input type="number" class="form-control" id="num_tkts" name="num_tkts" min="0" required>
                </div>
            </div>
            <div class="text-end">
                <button type="submit" class="btn btn-primary" {% if not current_user.CanAdd %}disabled{% endif %}>
                    <i class="bi bi-plus-circle me-2"></i>Add Transaction
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Transactions Table -->
<div class="card mb-4">
    <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
        <h2>Channel Transactions</h2>
        <div>
            <button class="btn btn-sm btn-light" id="printBtn">
                <i class="bi bi-printer"></i> Print
            </button>
            <button class="btn btn-sm btn-light" id="exportBtn">
                <i class="bi bi-file-excel"></i> Export
            </button>
        </div>
    </div>
    <div class="card-body">
        <!-- Filter Controls -->
        <div class="row mb-3">
            <div class="col-md-3">
                <label for="startDate" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="startDate">
            </div>
            <div class="col-md-3">
                <label for="endDate" class="form-label">End Date</label>
                <input type="date" class="form-control" id="endDate">
            </div>
            <div class="col-md-3">
                <label for="channelFilter" class="form-label">Channel</label>
                <select class="form-select" id="channelFilter">
                    <option value="">All Channels</option>
                    {% for channel in channels %}
                    <option value="{{ channel.Channel }}">{{ channel.Channel }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-primary w-100" id="filterDateBtn">
                    <i class="bi bi-filter"></i> Apply Filter
                </button>
            </div>
        </div>

        <!-- Transactions Table -->
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="transactionsTable">
                <thead>
                    <tr>
                        <th data-sort="id">ID <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="date">Date <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="channel">Channel <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th data-sort="tkts">TKTS <i class="bi bi-arrow-down-up sort-icon"></i></th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.ID }}</td>
                        <td>{{ transaction.Date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ transaction.Channel }}</td>
                        <td>{{ transaction.Num_TKTS }}</td>
                        <td>
                            {% if current_user.CanEdit %}
                            <button class="btn btn-sm btn-warning edit-btn" 
                                    data-id="{{ transaction.ID }}" 
                                    data-date="{{ transaction.Date.strftime('%Y-%m-%d') }}" 
                                    data-channel-id="{{ transaction.Ch_ID }}" 
                                    data-tkts="{{ transaction.Num_TKTS }}">
                                <i class="bi bi-pencil"></i> Edit
                            </button>
                            {% endif %}
                            {% if current_user.CanDelete %}
                            <button class="btn btn-sm btn-danger delete-btn" 
                                    data-id="{{ transaction.ID }}">
                                <i class="bi bi-trash"></i> Delete
                            </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr class="table-dark">
                        <td colspan="3" class="text-end"><strong>Total:</strong></td>
                        <td id="gridTotalTkts">0</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
    <div class="card-footer">
        <strong>Total Records: <span id="recordCount">{{ transactions|length }}</span></strong>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="editModalLabel">Edit Transaction</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" action="/channels/update" method="post">
                    <input type="hidden" id="edit_id" name="id">
                    <div class="mb-3">
                        <label for="edit_date" class="form-label">Date:</label>
                        <input type="date" class="form-control" id="edit_date" name="date" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_channel_id" class="form-label">Channel:</label>
                        <select class="form-select" id="edit_channel_id" name="channel_id" required>
                            {% for channel in channels %}
                            <option value="{{ channel.ChannelID }}">{{ channel.Channel }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_num_tkts" class="form-label">Number of TKTS:</label>
                        <input type="number" class="form-control" id="edit_num_tkts" name="num_tkts" min="0" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEditBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this transaction? This action cannot be undone.</p>
                <form id="deleteForm">
                    <input type="hidden" id="delete_id" name="id">
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // DOM elements
        const table = document.getElementById('transactionsTable');
        const startDateInput = document.getElementById('startDate');
        const endDateInput = document.getElementById('endDate');
        const channelFilter = document.getElementById('channelFilter');
        const filterDateBtn = document.getElementById('filterDateBtn');
        const recordCount = document.getElementById('recordCount');
        const gridTotalTkts = document.getElementById('gridTotalTkts');
        const editButtons = document.querySelectorAll('.edit-btn');
        const deleteButtons = document.querySelectorAll('.delete-btn');
        const saveEditBtn = document.getElementById('saveEditBtn');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const printBtn = document.getElementById('printBtn');
        const exportBtn = document.getElementById('exportBtn');
        
        // Bootstrap modals
        const editModal = new bootstrap.Modal(document.getElementById('editModal'));
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        
        // Find the latest transaction date
        let latestDate = new Date().toISOString().split('T')[0]; // Default to today
        if (table) {
            const dateColumn = 1; // Date is in the second column (index 1)
            const rows = table.querySelectorAll('tbody tr');
            if (rows.length > 0) {
                // Get all dates from the table
                const dates = Array.from(rows).map(row => new Date(row.cells[dateColumn].textContent));
                // Find the latest date
                if (dates.length > 0) {
                    const maxDate = new Date(Math.max.apply(null, dates));
                    latestDate = maxDate.toISOString().split('T')[0];
                }
            }
        }
        
        // Set date inputs for filter - both start and end date to latest transaction date
        if (startDateInput) {
            startDateInput.value = latestDate;
        }
        
        if (endDateInput) {
            endDateInput.value = latestDate;
        }
        
        // Set default date for new transaction to today
        const dateInput = document.getElementById('date');
        if (dateInput) {
            dateInput.value = new Date().toISOString().split('T')[0]; // Today's date
        }
        
        // Sorting state
        let currentSort = { column: 'date', direction: 'desc' };
        
        // Calculate initial totals
        calculateTotals();
        
        // Filter table by date range and channel
        function filterTable() {
            if (!table) return;
            
            const startDate = startDateInput ? new Date(startDateInput.value) : null;
            const endDate = endDateInput ? new Date(endDateInput.value) : null;
            const channelValue = channelFilter ? channelFilter.value : '';
            
            const rows = table.querySelectorAll('tbody tr');
            let visibleCount = 0;
            
            rows.forEach(row => {
                const dateCell = row.cells[1].textContent;
                const channelCell = row.cells[2].textContent;
                const rowDate = new Date(dateCell);
                
                let showRow = true;
                
                // Apply date filter
                if (startDate && endDate) {
                    // Set end date to end of day for inclusive comparison
                    const endOfDay = new Date(endDate);
                    endOfDay.setHours(23, 59, 59, 999);
                    
                    if (rowDate < startDate || rowDate > endOfDay) {
                        showRow = false;
                    }
                }
                
                // Apply channel filter
                if (channelValue && channelCell !== channelValue) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
                if (showRow) visibleCount++;
            });
            
            if (recordCount) {
                recordCount.textContent = visibleCount;
            }
            
            // Update totals after filtering
            calculateTotals();
        }
        
        // Calculate totals for visible rows
        function calculateTotals() {
            if (!table || !gridTotalTkts) return;
            
            const rows = Array.from(table.querySelectorAll('tbody tr'))
                .filter(row => row.style.display !== 'none');
            
            // Calculate total TKTs
            let totalTkts = 0;
            rows.forEach(row => {
                totalTkts += parseInt(row.cells[3].textContent) || 0;
            });
            
            // Update grid total
            gridTotalTkts.textContent = totalTkts;
        }
        
        // Sort table by column
        function sortTable(column) {
            if (!table) return;
            
            // Toggle direction if same column
            if (currentSort.column === column) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.column = column;
                currentSort.direction = 'asc';
            }
            
            const direction = currentSort.direction;
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            // Determine column index
            let columnIndex;
            switch (column) {
                case 'id': columnIndex = 0; break;
                case 'date': columnIndex = 1; break;
                case 'channel': columnIndex = 2; break;
                case 'tkts': columnIndex = 3; break;
                default: columnIndex = 0;
            }
            
            // Sort rows
            rows.sort((a, b) => {
                let aValue = a.cells[columnIndex].textContent.trim();
                let bValue = b.cells[columnIndex].textContent.trim();
                
                // Convert to appropriate types for comparison
                if (column === 'id' || column === 'tkts') {
                    aValue = parseInt(aValue) || 0;
                    bValue = parseInt(bValue) || 0;
                } else if (column === 'date') {
                    aValue = new Date(aValue);
                    bValue = new Date(bValue);
                }
                
                // Compare values
                if (aValue < bValue) return direction === 'asc' ? -1 : 1;
                if (aValue > bValue) return direction === 'asc' ? 1 : -1;
                return 0;
            });
            
            // Update table
            rows.forEach(row => tbody.appendChild(row));
            
            // Update sort indicators
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                const icon = header.querySelector('.sort-icon');
                if (header.getAttribute('data-sort') === column) {
                    icon.className = `bi bi-arrow-${direction === 'asc' ? 'up' : 'down'} sort-icon`;
                } else {
                    icon.className = 'bi bi-arrow-down-up sort-icon';
                }
            });
        }
        
        // Add event listeners
        
        // Filter button
        if (filterDateBtn) {
            filterDateBtn.addEventListener('click', filterTable);
        }
        
        // Sort headers
        const sortHeaders = table.querySelectorAll('th[data-sort]');
        sortHeaders.forEach(header => {
            header.addEventListener('click', function() {
                sortTable(this.getAttribute('data-sort'));
            });
        });
        
        // Edit buttons
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                const date = this.getAttribute('data-date');
                const channelId = this.getAttribute('data-channel-id');
                const tkts = this.getAttribute('data-tkts');
                
                document.getElementById('edit_id').value = id;
                document.getElementById('edit_date').value = date;
                document.getElementById('edit_channel_id').value = channelId;
                document.getElementById('edit_num_tkts').value = tkts;
                
                editModal.show();
            });
        });
        
        // Save edit button
        if (saveEditBtn) {
            saveEditBtn.addEventListener('click', function() {
                document.getElementById('editForm').submit();
            });
        }
        
        // Delete buttons
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                document.getElementById('delete_id').value = id;
                deleteModal.show();
            });
        });
        
        // Confirm delete button
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', function() {
                const id = document.getElementById('delete_id').value;
                
                fetch(`/delete/${id}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (response.ok) {
                        // Refresh the page to show updated data
                        window.location.reload();
                    } else {
                        return response.json().then(data => {
                            throw new Error(data.detail || 'Error deleting transaction');
                        });
                    }
                })
                .catch(error => {
                    alert('Error: ' + error.message);
                });
            });
        }
        
        // Print button
        if (printBtn) {
            printBtn.addEventListener('click', function() {
                window.print();
            });
        }
        
        // Export button
        if (exportBtn) {
            exportBtn.addEventListener('click', function() {
                // Get visible rows
                const rows = Array.from(table.querySelectorAll('tbody tr'))
                    .filter(row => row.style.display !== 'none');
                
                // Create CSV content
                let csv = 'ID,Date,Channel,TKTS\n';
                rows.forEach(row => {
                    const id = row.cells[0].textContent;
                    const date = row.cells[1].textContent;
                    const channel = row.cells[2].textContent;
                    const tkts = row.cells[3].textContent;
                    csv += `${id},"${date}","${channel}",${tkts}\n`;
                });
                
                // Create download link
                const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.setAttribute('href', url);
                link.setAttribute('download', 'channel_transactions.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            });
        }
        
        // Initial sort by date (descending)
        sortTable('date');
        
        // Automatically apply filter when page loads
        setTimeout(filterTable, 100);
    });
</script>
{% endblock %}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get form element
        const addForm = document.querySelector('form[action="/channels/submit"]');
        
        // Handle form submission via AJAX
        if (addForm) {
            addForm.addEventListener('submit', function(e) {
                e.preventDefault(); // Prevent default form submission
                
                const formData = new FormData(this);
                
                fetch('/channels/submit', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log("Add successful, reloading page...");
                        // Force reload the page to show updated data
                        window.location.reload(true);
                        
                        // Alternative approach if the above doesn't work
                        // setTimeout(function() {
                        //     window.location = window.location.href;
                        // }, 100);
                    } else {
                        alert('Error: ' + (data.detail || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error: ' + error.message);
                });
            });
        }
        
        // Edit form handling
        const saveEditBtn = document.getElementById('saveEditBtn');
        
        if (saveEditBtn) {
            saveEditBtn.addEventListener('click', function() {
                const form = document.getElementById('editForm');
                if (!form) return;
                
                const formData = new FormData(form);
                
                fetch('/update', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log("Edit successful, reloading page...");
                        // Close the modal
                        const editModal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
                        if (editModal) {
                            editModal.hide();
                        }
                        
                        // Force reload the page to show updated data
                        window.location.href = window.location.href;
                    } else {
                        alert('Error: ' + (data.detail || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error: ' + error.message);
                });
            });
        }
        
        // Delete confirmation
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        
        if (confirmDeleteBtn) {
            confirmDeleteBtn.addEventListener('click', function() {
                const id = document.getElementById('delete_id').value;
                
                fetch(`/delete/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close the modal
                        const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteModal'));
                        if (deleteModal) {
                            deleteModal.hide();
                        }
                        
                        // Reload the page to show updated data
                        window.location.reload();
                    } else {
                        alert('Error: ' + (data.detail || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error: ' + error.message);
                });
            });
        }
    });
</script>



