#!/usr/bin/env python3
"""
Test script to verify login functionality
"""

import requests
import sys

def test_login():
    print("Testing login functionality...")
    
    # Test login endpoint
    login_url = "http://localhost:8000/login"
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        print("Sending login request...")
        response = requests.post(login_url, data=login_data, allow_redirects=False)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 303:  # Redirect after successful login
            print("✅ Login successful - received redirect response")
            
            # Check if we got a token cookie
            if 'Set-Cookie' in response.headers:
                cookies = response.headers['Set-Cookie']
                if 'token=' in cookies:
                    print("✅ Token cookie set successfully")
                    return True
                else:
                    print("❌ No token cookie found in response")
                    return False
            else:
                print("❌ No cookies set in response")
                return False
        else:
            print(f"❌ Login failed with status code: {response.status_code}")
            print(f"Response content: {response.text[:500]}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure the server is running on localhost:8000")
        return False
    except Exception as e:
        print(f"❌ Login test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_login()
    sys.exit(0 if success else 1)
