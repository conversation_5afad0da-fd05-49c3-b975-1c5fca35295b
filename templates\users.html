{% extends "base.html" %}

{% block title %}User Management - Comsys Software Daily Reports Module{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-people-fill me-2"></i>User Management</h1>
    </div>

    <!-- Create User Card -->
    <div class="card mb-4 shadow">
        <div class="card-header bg-primary text-white">
            <h3><i class="bi bi-person-plus me-2"></i>Create New User</h3>
        </div>
        <div class="card-body">
            <form action="/users/create" method="post">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="d-flex gap-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_admin" name="is_admin">
                                <label class="form-check-label" for="is_admin">Admin</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="can_view" name="can_view" checked>
                                <label class="form-check-label" for="can_view">View</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="can_add" name="can_add">
                                <label class="form-check-label" for="can_add">Add</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="can_edit" name="can_edit">
                                <label class="form-check-label" for="can_edit">Edit</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="can_delete" name="can_delete">
                                <label class="form-check-label" for="can_delete">Delete</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="text-end">
                    <button type="submit" class="btn btn-primary">Create User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Users List -->
    <div class="card shadow">
        <div class="card-header bg-secondary text-white">
            <h3><i class="bi bi-list-ul me-2"></i>Users</h3>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Status</th>
                            <th>Admin</th>
                            <th>Permissions</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.UserID }}</td>
                            <td>{{ user.UserName }}</td>
                            <td>{{ user.Email }}</td>
                            <td>
                                {% if user.IsActive %}
                                <span class="badge bg-success">Active</span>
                                {% else %}
                                <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.IsAdmin %}
                                <span class="badge bg-primary">Admin</span>
                                {% else %}
                                <span class="badge bg-secondary">User</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    {% if user.CanView %}
                                    <span class="badge bg-info">View</span>
                                    {% endif %}
                                    {% if user.CanAdd %}
                                    <span class="badge bg-success">Add</span>
                                    {% endif %}
                                    {% if user.CanEdit %}
                                    <span class="badge bg-warning">Edit</span>
                                    {% endif %}
                                    {% if user.CanDelete %}
                                    <span class="badge bg-danger">Delete</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-sm btn-primary edit-user" 
                                            data-id="{{ user.UserID }}"
                                            data-username="{{ user.UserName }}"
                                            data-email="{{ user.Email }}"
                                            data-active="{{ user.IsActive }}"
                                            data-admin="{{ user.IsAdmin }}"
                                            data-view="{{ user.CanView }}"
                                            data-add="{{ user.CanAdd }}"
                                            data-edit="{{ user.CanEdit }}"
                                            data-delete="{{ user.CanDelete }}">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-warning reset-password" data-id="{{ user.UserID }}" data-username="{{ user.UserName }}">
                                        <i class="bi bi-key"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger delete-user" data-id="{{ user.UserID }}" data-username="{{ user.UserName }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm" method="post">
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">Active</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_admin" name="is_admin">
                            <label class="form-check-label" for="edit_is_admin">Admin</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="d-flex flex-column gap-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_can_view" name="can_view">
                                <label class="form-check-label" for="edit_can_view">View</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_can_add" name="can_add">
                                <label class="form-check-label" for="edit_can_add">Add</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_can_edit" name="can_edit">
                                <label class="form-check-label" for="edit_can_edit">Edit</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="edit_can_delete" name="can_delete">
                                <label class="form-check-label" for="edit_can_delete">Delete</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveUserBtn">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="resetPasswordModalLabel">Reset Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Reset password for user: <strong id="resetUsername"></strong></p>
                <form id="resetPasswordForm" method="post">
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="password" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmResetBtn">Reset Password</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteUserModalLabel">Delete User</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user: <strong id="deleteUsername"></strong>?</p>
                <p class="text-danger"><strong>Warning:</strong> This action cannot be undone.</p>
                <form id="deleteUserForm" method="post">
                    <!-- Form will be populated with action via JavaScript -->
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteUserBtn">Delete User</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Edit User
        const editButtons = document.querySelectorAll('.edit-user');
        const editForm = document.getElementById('editUserForm');
        const saveUserBtn = document.getElementById('saveUserBtn');
        
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const userId = this.getAttribute('data-id');
                const username = this.getAttribute('data-username');
                const email = this.getAttribute('data-email');
                const isActive = this.getAttribute('data-active') === 'True';
                const isAdmin = this.getAttribute('data-admin') === 'True';
                const canView = this.getAttribute('data-view') === 'True';
                const canAdd = this.getAttribute('data-add') === 'True';
                const canEdit = this.getAttribute('data-edit') === 'True';
                const canDelete = this.getAttribute('data-delete') === 'True';
                
                // Set form action
                editForm.action = `/users/update/${userId}`;
                
                // Set form values
                document.getElementById('edit_username').value = username;
                document.getElementById('edit_email').value = email;
                document.getElementById('edit_is_active').checked = isActive;
                document.getElementById('edit_is_admin').checked = isAdmin;
                document.getElementById('edit_can_view').checked = canView;
                document.getElementById('edit_can_add').checked = canAdd;
                document.getElementById('edit_can_edit').checked = canEdit;
                document.getElementById('edit_can_delete').checked = canDelete;
                
                // Show modal
                const editModal = new bootstrap.Modal(document.getElementById('editUserModal'));
                editModal.show();
            });
        });
        
        saveUserBtn.addEventListener('click', function() {
            editForm.submit();
        });
        
        // Reset Password
        const resetButtons = document.querySelectorAll('.reset-password');
        const resetForm = document.getElementById('resetPasswordForm');
        const confirmResetBtn = document.getElementById('confirmResetBtn');
        
        resetButtons.forEach(button => {
            button.addEventListener('click', function() {
                const userId = this.getAttribute('data-id');
                const username = this.getAttribute('data-username');
                
                // Set form action
                resetForm.action = `/users/reset-password/${userId}`;
                
                // Set username in modal
                document.getElementById('resetUsername').textContent = username;
                
                // Show modal
                const resetModal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
                resetModal.show();
            });
        });
        
        confirmResetBtn.addEventListener('click', function() {
            resetForm.submit();
        });
        
        // Delete User
        const deleteButtons = document.querySelectorAll('.delete-user');
        const deleteForm = document.getElementById('deleteUserForm');
        const confirmDeleteBtn = document.getElementById('confirmDeleteUserBtn');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const userId = this.getAttribute('data-id');
                const username = this.getAttribute('data-username');
                
                // Set form action
                deleteForm.action = `/users/delete/${userId}`;
                
                // Set username in modal
                document.getElementById('deleteUsername').textContent = username;
                
                // Show modal
                const deleteModal = new bootstrap.Modal(document.getElementById('deleteUserModal'));
                deleteModal.show();
            });
        });
        
        confirmDeleteBtn.addEventListener('click', function() {
            // Use fetch API for delete operation
            const formAction = deleteForm.action;
            
            fetch(formAction, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (response.ok) {
                    // Refresh the page to show updated user list
                    window.location.reload();
                } else {
                    return response.json().then(data => {
                        throw new Error(data.detail || 'Error deleting user');
                    });
                }
            })
            .catch(error => {
                alert('Error: ' + error.message);
            });
        });
    });
</script>
{% endblock %}





